/**
 * PROTEC Alumni App - Design System Colors
 *
 * This file defines the comprehensive color system for the PROTEC Alumni mobile app,
 * including brand colors, semantic colors, and accessibility-compliant color schemes
 * for both light and dark modes.
 */

// PROTEC Brand Colors
export const BrandColors = {
  // Primary brand colors
  navy: '#012A5B',        // PROTEC Navy - Primary brand color
  red: '#E31E24',         // PROTEC Red - Accent color

  // Extended brand palette
  navyLight: '#1E3A6F',   // Lighter navy for hover states
  navyDark: '#001A3D',    // Darker navy for pressed states
  redLight: '#FF4A50',    // Lighter red for hover states
  redDark: '#B71C1C',     // Darker red for pressed states

  // Neutral colors
  white: '#FFFFFF',
  black: '#000000',
  gray50: '#F9FAFB',
  gray100: '#F3F4F6',
  gray200: '#E5E7EB',
  gray300: '#D1D5DB',
  gray400: '#9CA3AF',
  gray500: '#6B7280',
  gray600: '#4B5563',
  gray700: '#374151',
  gray800: '#1F2937',
  gray900: '#111827',
} as const;

// Semantic color mappings for light and dark themes
const tintColorLight = BrandColors.navy;
const tintColorDark = BrandColors.white;

export const Colors = {
  light: {
    // Text colors
    text: BrandColors.gray900,
    textSecondary: BrandColors.gray600,
    textMuted: BrandColors.gray500,
    textInverse: BrandColors.white,

    // Background colors
    background: BrandColors.white,
    backgroundSecondary: BrandColors.gray50,
    backgroundMuted: BrandColors.gray100,

    // Brand colors
    primary: BrandColors.navy,
    primaryLight: BrandColors.navyLight,
    primaryDark: BrandColors.navyDark,
    accent: BrandColors.red,
    accentLight: BrandColors.redLight,
    accentDark: BrandColors.redDark,

    // UI element colors
    tint: tintColorLight,
    border: BrandColors.gray200,
    borderMuted: BrandColors.gray100,

    // Icon colors
    icon: BrandColors.gray500,
    iconMuted: BrandColors.gray400,

    // Tab colors
    tabIconDefault: BrandColors.gray400,
    tabIconSelected: tintColorLight,

    // Status colors
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',

    // Interactive states
    hover: BrandColors.gray50,
    pressed: BrandColors.gray100,
    disabled: BrandColors.gray300,

    // Card and surface colors
    card: BrandColors.white,
    cardBorder: BrandColors.gray200,
    surface: BrandColors.gray50,
  },
  dark: {
    // Text colors
    text: BrandColors.gray100,
    textSecondary: BrandColors.gray300,
    textMuted: BrandColors.gray400,
    textInverse: BrandColors.gray900,

    // Background colors
    background: BrandColors.gray900,
    backgroundSecondary: BrandColors.gray800,
    backgroundMuted: BrandColors.gray700,

    // Brand colors (adjusted for dark mode)
    primary: '#4A90E2',      // Lighter blue for better contrast
    primaryLight: '#6BA3F0',
    primaryDark: '#2E5A87',
    accent: '#FF6B6B',       // Softer red for dark mode
    accentLight: '#FF8E8E',
    accentDark: '#E55555',

    // UI element colors
    tint: tintColorDark,
    border: BrandColors.gray600,
    borderMuted: BrandColors.gray700,

    // Icon colors
    icon: BrandColors.gray400,
    iconMuted: BrandColors.gray500,

    // Tab colors
    tabIconDefault: BrandColors.gray500,
    tabIconSelected: tintColorDark,

    // Status colors (adjusted for dark mode)
    success: '#34D399',
    warning: '#FBBF24',
    error: '#F87171',
    info: '#60A5FA',

    // Interactive states
    hover: BrandColors.gray800,
    pressed: BrandColors.gray700,
    disabled: BrandColors.gray600,

    // Card and surface colors
    card: BrandColors.gray800,
    cardBorder: BrandColors.gray600,
    surface: BrandColors.gray700,
  },
} as const;

// Typography scale
export const Typography = {
  // Font sizes
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },

  // Line heights
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },

  // Font weights
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },
} as const;

// Spacing scale (based on 4px grid)
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,
  '3xl': 32,
  '4xl': 40,
  '5xl': 48,
  '6xl': 64,
} as const;

// Border radius scale
export const BorderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 24,
  full: 9999,
} as const;

// Shadow definitions
export const Shadows = {
  sm: {
    shadowColor: BrandColors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  md: {
    shadowColor: BrandColors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  lg: {
    shadowColor: BrandColors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  xl: {
    shadowColor: BrandColors.black,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  '2xl': {
    shadowColor: BrandColors.black,
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 12,
  },
} as const;
