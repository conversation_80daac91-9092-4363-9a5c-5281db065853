import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Dimensions,
  Image,
  TouchableOpacity,
} from 'react-native';
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { DesignSystem } from '@/constants/DesignSystem';
import { validateEmail } from '@/lib/utils/validation';

import { AnimatedView, ScaleTransition, ShakeView } from '@/components/animated/AnimatedComponents';
import { getAccessibilityProps, announceForAccessibility } from '@/lib/utils/accessibility';
import { getResponsiveLayout } from '@/lib/utils/responsive';

const { height, width } = Dimensions.get('window');

export default function RegisterScreen() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    studentId: '',
    graduationYear: '',
    course: '',
    password: '',
    confirmPassword: '',
  });
  
  const [errors, setErrors] = useState({
    firstName: '',
    lastName: '',
    email: '',
    studentId: '',
    graduationYear: '',
    course: '',
    password: '',
    confirmPassword: '',
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);

  const isTablet = width > 768;

  const validateForm = () => {
    const newErrors = {
      firstName: '',
      lastName: '',
      email: '',
      studentId: '',
      graduationYear: '',
      course: '',
      password: '',
      confirmPassword: '',
    };

    let isValid = true;

    // First Name validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
      isValid = false;
    }

    // Last Name validation
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
      isValid = false;
    }

    // Email validation
    const emailValidation = validateEmail(formData.email);
    if (!emailValidation.isValid) {
      newErrors.email = emailValidation.error || 'Invalid email';
      isValid = false;
    }

    // Student ID validation
    if (!formData.studentId.trim()) {
      newErrors.studentId = 'Student ID is required';
      isValid = false;
    }

    // Graduation Year validation
    if (!formData.graduationYear.trim()) {
      newErrors.graduationYear = 'Graduation year is required';
      isValid = false;
    } else {
      const year = parseInt(formData.graduationYear);
      const currentYear = new Date().getFullYear();
      if (year < 1970 || year > currentYear + 10) {
        newErrors.graduationYear = 'Please enter a valid graduation year';
        isValid = false;
      }
    }

    // Course validation
    if (!formData.course.trim()) {
      newErrors.course = 'Course/Program is required';
      isValid = false;
    }

    // Password validation
    if (!formData.password.trim()) {
      newErrors.password = 'Password is required';
      isValid = false;
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
      isValid = false;
    }

    // Confirm Password validation
    if (!formData.confirmPassword.trim()) {
      newErrors.confirmPassword = 'Please confirm your password';
      isValid = false;
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setShowSuccess(true);
      announceForAccessibility('Registration request submitted successfully');
      
      setTimeout(() => {
        router.push('/auth/login');
      }, 2000);
      
    } catch (error) {
      console.error('Registration error:', error);
      Alert.alert('Error', 'Registration failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const nextStep = () => {
    if (currentStep === 1) {
      // Validate first step
      const step1Valid = formData.firstName.trim() && 
                        formData.lastName.trim() && 
                        validateEmail(formData.email).isValid;
      
      if (!step1Valid) {
        Alert.alert('Please complete all required fields in this step');
        return;
      }
    }
    
    if (currentStep < 2) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingSpinner 
          variant="dots" 
          size="large" 
          text="Processing your registration..."
        />
      </View>
    );
  }

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <LinearGradient
        colors={[
          DesignSystem.Colors.light.success,
          DesignSystem.Colors.light.primary,
        ]}
        style={StyleSheet.absoluteFill}
      />
      
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <View style={isTablet ? styles.tabletContainer : styles.content}>
          {/* Left Column - Branding (Tablet only) */}
          {isTablet && (
            <AnimatedView animation="slideLeft" delay={200} style={styles.leftColumn}>
              <View style={styles.brandingSection}>
                <AnimatedView animation="scaleIn" delay={400} style={styles.logoContainer}>
                  <Image
                    source={require('@/assets/images/icon.png')}
                    style={styles.logo}
                    resizeMode="contain"
                  />
                </AnimatedView>
                
                <AnimatedView animation="fadeIn" delay={600}>
                  <ThemedText style={styles.brandTitle}>
                    Join PROTEC
                  </ThemedText>
                </AnimatedView>
                
                <AnimatedView animation="fadeIn" delay={800}>
                  <ThemedText style={styles.brandSubtitle}>
                    Connect with fellow alumni and grow your professional network
                  </ThemedText>
                </AnimatedView>
                
                <AnimatedView animation="fadeIn" delay={1000}>
                  <View style={styles.benefitsList}>
                    <View style={styles.benefitItem}>
                      <ThemedText style={styles.benefitIcon}>✨</ThemedText>
                      <ThemedText style={styles.benefitText}>Exclusive alumni network access</ThemedText>
                    </View>
                    <View style={styles.benefitItem}>
                      <ThemedText style={styles.benefitIcon}>🚀</ThemedText>
                      <ThemedText style={styles.benefitText}>Career advancement opportunities</ThemedText>
                    </View>
                    <View style={styles.benefitItem}>
                      <ThemedText style={styles.benefitIcon}>🎯</ThemedText>
                      <ThemedText style={styles.benefitText}>Professional development resources</ThemedText>
                    </View>
                    <View style={styles.benefitItem}>
                      <ThemedText style={styles.benefitIcon}>💡</ThemedText>
                      <ThemedText style={styles.benefitText}>Mentorship programs</ThemedText>
                    </View>
                  </View>
                </AnimatedView>
              </View>
            </AnimatedView>
          )}

          {/* Right Column - Form */}
          <AnimatedView 
            animation="slideRight" 
            delay={isTablet ? 300 : 200} 
            style={isTablet ? styles.rightColumn : styles.mobileColumn}
          >
            <View style={styles.formCard}>
              {/* Mobile Header */}
              {!isTablet && (
                <AnimatedView animation="fadeIn" delay={400} style={styles.mobileHeader}>
                  <AnimatedView animation="scaleIn" delay={600} style={styles.mobileLogoContainer}>
                    <Image
                      source={require('@/assets/images/icon.png')}
                      style={styles.mobileLogo}
                      resizeMode="contain"
                    />
                  </AnimatedView>
                  <ThemedText style={styles.mobileTitle}>Join PROTEC</ThemedText>
                  <ThemedText style={styles.mobileSubtitle}>
                    Create your alumni account
                  </ThemedText>
                </AnimatedView>
              )}

              {/* Tablet Header */}
              {isTablet && (
                <AnimatedView animation="fadeIn" delay={600} style={styles.tabletHeader}>
                  <ThemedText style={styles.tabletTitle}>Create Account</ThemedText>
                  <ThemedText style={styles.tabletSubtitle}>
                    Join the PROTEC alumni community
                  </ThemedText>
                </AnimatedView>
              )}

              {/* Progress Indicator */}
              <View style={styles.progressContainer}>
                <View style={styles.progressBar}>
                  <View style={[styles.progressFill, { width: `${(currentStep / 2) * 100}%` }]} />
                </View>
                <ThemedText style={styles.progressText}>
                  Step {currentStep} of 2
                </ThemedText>
              </View>

              {/* Success Message */}
              <ScaleTransition visible={showSuccess}>
                <View style={styles.successContainer}>
                  <ThemedText style={styles.successText}>
                    ✅ Registration submitted! You'll receive an email once your account is approved.
                  </ThemedText>
                </View>
              </ScaleTransition>

              {/* Form */}
              <AnimatedView animation="fadeIn" delay={800} style={styles.form}>
                {currentStep === 1 && (
                  <View style={styles.stepContainer}>
                    <ThemedText style={styles.stepTitle}>Personal Information</ThemedText>
                    
                    <View style={styles.rowContainer}>
                      <ShakeView trigger={!!errors.firstName} intensity={5} style={styles.halfWidth}>
                        <Input
                          label="First Name"
                          value={formData.firstName}
                          onChangeText={(value) => handleInputChange('firstName', value)}
                          placeholder="Enter your first name"
                          error={errors.firstName}
                          variant="outlined"
                          size="lg"
                        />
                      </ShakeView>
                      
                      <ShakeView trigger={!!errors.lastName} intensity={5} style={styles.halfWidth}>
                        <Input
                          label="Last Name"
                          value={formData.lastName}
                          onChangeText={(value) => handleInputChange('lastName', value)}
                          placeholder="Enter your last name"
                          error={errors.lastName}
                          variant="outlined"
                          size="lg"
                        />
                      </ShakeView>
                    </View>

                    <ShakeView trigger={!!errors.email} intensity={5}>
                      <Input
                        label="Email Address"
                        value={formData.email}
                        onChangeText={(value) => handleInputChange('email', value)}
                        placeholder="Enter your email"
                        keyboardType="email-address"
                        autoCapitalize="none"
                        autoCorrect={false}
                        error={errors.email}
                        variant="outlined"
                        size="lg"
                      />
                    </ShakeView>
                  </View>
                )}

                {currentStep === 2 && (
                  <View style={styles.stepContainer}>
                    <ThemedText style={styles.stepTitle}>Academic & Account Details</ThemedText>
                    
                    <View style={styles.rowContainer}>
                      <ShakeView trigger={!!errors.studentId} intensity={5} style={styles.halfWidth}>
                        <Input
                          label="Student ID"
                          value={formData.studentId}
                          onChangeText={(value) => handleInputChange('studentId', value)}
                          placeholder="Your student ID"
                          error={errors.studentId}
                          variant="outlined"
                          size="lg"
                        />
                      </ShakeView>
                      
                      <ShakeView trigger={!!errors.graduationYear} intensity={5} style={styles.halfWidth}>
                        <Input
                          label="Graduation Year"
                          value={formData.graduationYear}
                          onChangeText={(value) => handleInputChange('graduationYear', value)}
                          placeholder="e.g., 2023"
                          keyboardType="numeric"
                          error={errors.graduationYear}
                          variant="outlined"
                          size="lg"
                        />
                      </ShakeView>
                    </View>

                    <ShakeView trigger={!!errors.course} intensity={5}>
                      <Input
                        label="Course/Program"
                        value={formData.course}
                        onChangeText={(value) => handleInputChange('course', value)}
                        placeholder="e.g., Software Development"
                        error={errors.course}
                        variant="outlined"
                        size="lg"
                      />
                    </ShakeView>

                    <ShakeView trigger={!!errors.password} intensity={5}>
                      <Input
                        label="Password"
                        value={formData.password}
                        onChangeText={(value) => handleInputChange('password', value)}
                        placeholder="Create a secure password"
                        secureTextEntry
                        error={errors.password}
                        variant="outlined"
                        size="lg"
                      />
                    </ShakeView>

                    <ShakeView trigger={!!errors.confirmPassword} intensity={5}>
                      <Input
                        label="Confirm Password"
                        value={formData.confirmPassword}
                        onChangeText={(value) => handleInputChange('confirmPassword', value)}
                        placeholder="Confirm your password"
                        secureTextEntry
                        error={errors.confirmPassword}
                        variant="outlined"
                        size="lg"
                      />
                    </ShakeView>
                  </View>
                )}

                {/* Navigation Buttons */}
                <View style={styles.buttonContainer}>
                  {currentStep > 1 && (
                    <Button
                      title="Previous"
                      onPress={prevStep}
                      variant="outline"
                      size="lg"
                      style={styles.navButton}
                    />
                  )}
                  
                  {currentStep < 2 ? (
                    <Button
                      title="Next"
                      onPress={nextStep}
                      variant="primary"
                      size="lg"
                      fullWidth={currentStep === 1}
                      style={currentStep === 1 ? styles.fullButton : styles.navButton}
                    />
                  ) : (
                    <Button
                      title="Submit Registration"
                      onPress={handleSubmit}
                      disabled={isLoading}
                      loading={isLoading}
                      variant="primary"
                      size="lg"
                      style={styles.navButton}
                    />
                  )}
                </View>
              </AnimatedView>

              {/* Footer */}
              <View style={styles.footer}>
                <ThemedText style={styles.footerText}>
                  Already have an account?{' '}
                  <TouchableOpacity onPress={() => router.push('/auth/login')}>
                    <ThemedText style={styles.footerLink}>
                      Sign in here
                    </ThemedText>
                  </TouchableOpacity>
                </ThemedText>
              </View>
            </View>
          </AnimatedView>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: DesignSystem.Colors.light.background,
  },

  scrollContent: {
    flexGrow: 1,
    minHeight: height,
  },

  // Mobile Layout
  content: {
    flex: 1,
    paddingHorizontal: DesignSystem.Spacing['3xl'],
    paddingTop: DesignSystem.Spacing['6xl'],
    paddingBottom: DesignSystem.Spacing['4xl'],
    maxWidth: 400,
    alignSelf: 'center',
    width: '100%',
  },

  // Tablet Layout
  tabletContainer: {
    flex: 1,
    flexDirection: 'row',
    minHeight: height,
  },

  leftColumn: {
    flex: 1,
    padding: DesignSystem.Spacing['6xl'],
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },

  rightColumn: {
    flex: 1,
    padding: DesignSystem.Spacing['6xl'],
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },

  mobileColumn: {
    flex: 1,
    padding: DesignSystem.Spacing['3xl'],
    justifyContent: 'center',
  },

  // Form Card
  formCard: {
    backgroundColor: DesignSystem.Colors.light.background,
    borderRadius: DesignSystem.BorderRadius['2xl'],
    padding: DesignSystem.Spacing['4xl'],
    width: '100%',
    maxWidth: 500,
    ...DesignSystem.Shadows['2xl'],
  },

  // Branding Section (Tablet)
  brandingSection: {
    alignItems: 'center',
    maxWidth: 400,
  },

  brandTitle: {
    fontSize: 48,
    fontWeight: '900',
    color: DesignSystem.Colors.light.textInverse,
    textAlign: 'center',
    marginBottom: DesignSystem.Spacing['2xl'],
    letterSpacing: -2,
  },

  brandSubtitle: {
    fontSize: 20,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 28,
    marginBottom: DesignSystem.Spacing['4xl'],
  },

  benefitsList: {
    alignItems: 'flex-start',
    width: '100%',
  },

  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: DesignSystem.Spacing['2xl'],
  },

  benefitIcon: {
    fontSize: 24,
    marginRight: DesignSystem.Spacing.lg,
  },

  benefitText: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: DesignSystem.Typography.fontWeight.medium,
  },

  // Headers
  mobileHeader: {
    alignItems: 'center',
    marginBottom: DesignSystem.Spacing['3xl'],
  },

  tabletHeader: {
    alignItems: 'center',
    marginBottom: DesignSystem.Spacing['2xl'],
  },

  mobileLogoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: DesignSystem.Colors.light.success,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: DesignSystem.Spacing['2xl'],
    ...DesignSystem.Shadows.lg,
  },

  logoContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: DesignSystem.Spacing['3xl'],
    ...DesignSystem.Shadows.lg,
  },

  mobileLogo: {
    width: 50,
    height: 50,
  },

  logo: {
    width: 80,
    height: 80,
  },

  mobileTitle: {
    fontSize: 28,
    fontWeight: '800',
    textAlign: 'center',
    marginBottom: DesignSystem.Spacing.md,
    color: DesignSystem.Colors.light.text,
  },

  tabletTitle: {
    fontSize: 32,
    fontWeight: '800',
    textAlign: 'center',
    marginBottom: DesignSystem.Spacing.md,
    color: DesignSystem.Colors.light.text,
  },

  mobileSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: DesignSystem.Colors.light.textSecondary,
    lineHeight: 24,
  },

  tabletSubtitle: {
    fontSize: 18,
    textAlign: 'center',
    color: DesignSystem.Colors.light.textSecondary,
    lineHeight: 26,
  },

  // Progress
  progressContainer: {
    marginBottom: DesignSystem.Spacing['2xl'],
  },

  progressBar: {
    height: 4,
    backgroundColor: DesignSystem.Colors.light.backgroundSecondary,
    borderRadius: 2,
    marginBottom: DesignSystem.Spacing.sm,
  },

  progressFill: {
    height: '100%',
    backgroundColor: DesignSystem.Colors.light.success,
    borderRadius: 2,
  },

  progressText: {
    fontSize: DesignSystem.Typography.fontSize.sm,
    color: DesignSystem.Colors.light.textSecondary,
    textAlign: 'center',
  },

  // Form Elements
  successContainer: {
    backgroundColor: DesignSystem.Colors.light.success,
    paddingHorizontal: DesignSystem.Spacing.lg,
    paddingVertical: DesignSystem.Spacing.md,
    borderRadius: DesignSystem.BorderRadius.lg,
    marginBottom: DesignSystem.Spacing.lg,
    alignItems: 'center',
  },

  successText: {
    color: DesignSystem.Colors.light.textInverse,
    fontWeight: DesignSystem.Typography.fontWeight.medium,
    textAlign: 'center',
  },

  form: {
    width: '100%',
  },

  stepContainer: {
    marginBottom: DesignSystem.Spacing['2xl'],
  },

  stepTitle: {
    fontSize: 20,
    fontWeight: DesignSystem.Typography.fontWeight.semibold,
    color: DesignSystem.Colors.light.text,
    marginBottom: DesignSystem.Spacing.lg,
  },

  rowContainer: {
    flexDirection: 'row',
    gap: DesignSystem.Spacing.md,
  },

  halfWidth: {
    flex: 1,
  },

  buttonContainer: {
    flexDirection: 'row',
    gap: DesignSystem.Spacing.md,
    marginTop: DesignSystem.Spacing.lg,
  },

  navButton: {
    flex: 1,
  },

  fullButton: {
    width: '100%',
  },

  footer: {
    alignItems: 'center',
    marginTop: DesignSystem.Spacing.lg,
  },

  footerText: {
    fontSize: DesignSystem.Typography.fontSize.sm,
    color: DesignSystem.Colors.light.textMuted,
    textAlign: 'center',
    lineHeight: 20,
  },

  footerLink: {
    color: DesignSystem.Colors.light.success,
    fontWeight: DesignSystem.Typography.fontWeight.medium,
  },
});