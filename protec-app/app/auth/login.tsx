import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Dimensions,
  Image,
  TouchableOpacity,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import {
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useAuth } from '@/lib/providers/AuthProvider';
import { useDeepLinking } from '@/lib/hooks/useDeepLinking';
import { DesignSystem } from '@/constants/DesignSystem';
import { validateEmail } from '@/lib/utils/validation';

import { AnimatedView, ScaleTransition, ShakeView } from '@/components/animated/AnimatedComponents';
import { getAccessibilityProps, announceForAccessibility } from '@/lib/utils/accessibility';
import { getResponsiveLayout } from '@/lib/utils/responsive';

const { height, width } = Dimensions.get('window');

export default function LoginScreen() {
  const { email: paramEmail } = useLocalSearchParams<{ email?: string }>();
  const [email, setEmail] = useState(paramEmail || '');
  const [password, setPassword] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [useCredentials, setUseCredentials] = useState(true);

  // Responsive layout
  const responsiveLayout = getResponsiveLayout();

  // Animation values for success message
  const successScale = useSharedValue(0);

  // Auth context
  const { signInWithCredentials, signInWithMagicLink, signInWithGoogle } = useAuth();

  // Initialize deep linking
  useDeepLinking();

  useEffect(() => {
    // Clean up expired magic link tokens on app start
    const { MagicLinkService } = require('@/services/magicLinkService');
    MagicLinkService.cleanupExpiredTokens();
  }, []);

  const validateEmailField = (emailValue: string): boolean => {
    const result = validateEmail(emailValue);
    if (!result.isValid) {
      setEmailError(result.error || 'Invalid email');
      return false;
    }
    setEmailError('');
    return true;
  };

  const handleEmailChange = (value: string) => {
    setEmail(value);
    if (emailError) {
      // Clear error when user starts typing
      setEmailError('');
    }
  };

  const handlePasswordChange = (value: string) => {
    setPassword(value);
    if (passwordError) {
      // Clear error when user starts typing
      setPasswordError('');
    }
  };

  const validatePassword = (password: string): boolean => {
    return password.length >= 8;
  };

  const handleCredentialsLogin = async () => {
    // Validate inputs
    if (!validateEmailField(email)) {
      return;
    }

    if (!password.trim()) {
      setPasswordError('Password is required');
      return;
    }

    if (!validatePassword(password)) {
      setPasswordError('Password must be at least 8 characters');
      return;
    }

    setIsLoading(true);
    try {
      console.log('Signing in with credentials:', email);

      const result = await signInWithCredentials(email.trim(), password);

      if (!result.success) {
        announceForAccessibility(`Error: ${result.error}`);
        Alert.alert('Error', result.error || 'Failed to sign in', [{ text: 'OK' }]);
      }
      // Success navigation is handled by auth provider
    } catch (error) {
      console.error('Credentials login error:', error);
      Alert.alert('Error', 'Failed to sign in. Please try again.', [{ text: 'OK' }]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmailLogin = async () => {
    if (!validateEmailField(email)) {
      return;
    }

    setIsLoading(true);
    try {
      console.log('Sending magic link to:', email);

      const result = await signInWithMagicLink(email.trim());

      if (result.success) {
        setShowSuccess(true);
        successScale.value = withSpring(1, DesignSystem.Animations.spring.bouncy);

        // Announce success to screen readers
        announceForAccessibility('Magic link sent successfully. Please check your email.');

        // Auto-hide success message after 3 seconds
        setTimeout(() => {
          setShowSuccess(false);
          successScale.value = withTiming(0, { duration: 300 });
          setEmail('');
        }, 3000);
      } else {
        announceForAccessibility(`Error: ${result.error}`);
        Alert.alert('Error', result.error || 'Failed to send magic link', [{ text: 'OK' }]);
      }
    } catch (error) {
      console.error('Magic link error:', error);
      Alert.alert('Error', 'Failed to send magic link. Please try again.', [{ text: 'OK' }]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    try {
      setIsLoading(true);
      const result = await signInWithGoogle();

      if (!result.success) {
        Alert.alert('Error', result.error || 'Google sign in failed');
      }
      // Success navigation is handled by auth provider
    } catch (error) {
      console.error('Google login error:', error);
      Alert.alert('Error', 'Failed to sign in with Google. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGitHubLogin = () => {
    Alert.alert('Coming Soon', 'GitHub login will be available soon!');
  };



  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingSpinner 
          variant="dots" 
          size="large" 
          text="Sending magic link..."
        />
      </View>
    );
  }

  const isTablet = width > 768;

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <LinearGradient
        colors={[
          DesignSystem.Colors.light.primary,
          DesignSystem.Colors.light.primaryDark,
        ]}
        style={StyleSheet.absoluteFill}
      />
      
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <View style={isTablet ? styles.tabletContainer : styles.content}>
          {/* Left Column - Branding (Tablet only) */}
          {isTablet && (
            <AnimatedView animation="slideLeft" delay={200} style={styles.leftColumn}>
              <View style={styles.brandingSection}>
                <AnimatedView animation="scaleIn" delay={400} style={styles.logoContainer}>
                  <Image
                    source={require('@/assets/images/icon.png')}
                    style={styles.logo}
                    resizeMode="contain"
                  />
                </AnimatedView>
                
                <AnimatedView animation="fadeIn" delay={600}>
                  <ThemedText style={styles.brandTitle}>
                    PROTEC Alumni
                  </ThemedText>
                </AnimatedView>
                
                <AnimatedView animation="fadeIn" delay={800}>
                  <ThemedText style={styles.brandSubtitle}>
                    Connect with your alumni network and build lasting professional relationships
                  </ThemedText>
                </AnimatedView>
                
                <AnimatedView animation="fadeIn" delay={1000}>
                  <View style={styles.featuresList}>
                    <View style={styles.featureItem}>
                      <ThemedText style={styles.featureIcon}>🎓</ThemedText>
                      <ThemedText style={styles.featureText}>Alumni Network</ThemedText>
                    </View>
                    <View style={styles.featureItem}>
                      <ThemedText style={styles.featureIcon}>💼</ThemedText>
                      <ThemedText style={styles.featureText}>Career Development</ThemedText>
                    </View>
                    <View style={styles.featureItem}>
                      <ThemedText style={styles.featureIcon}>🤝</ThemedText>
                      <ThemedText style={styles.featureText}>Networking Events</ThemedText>
                    </View>
                    <View style={styles.featureItem}>
                      <ThemedText style={styles.featureIcon}>💝</ThemedText>
                      <ThemedText style={styles.featureText}>Support Community</ThemedText>
                    </View>
                  </View>
                </AnimatedView>
              </View>
            </AnimatedView>
          )}

          {/* Right Column - Form */}
          <AnimatedView 
            animation="slideRight" 
            delay={isTablet ? 300 : 200} 
            style={isTablet ? styles.rightColumn : styles.mobileColumn}
          >
            <View style={styles.formCard}>
              {/* Mobile Header */}
              {!isTablet && (
                <AnimatedView animation="fadeIn" delay={400} style={styles.mobileHeader}>
                  <AnimatedView animation="scaleIn" delay={600} style={styles.mobileLogoContainer}>
                    <Image
                      source={require('@/assets/images/icon.png')}
                      style={styles.mobileLogo}
                      resizeMode="contain"
                    />
                  </AnimatedView>
                  <ThemedText style={styles.mobileTitle}>Welcome Back</ThemedText>
                  <ThemedText style={styles.mobileSubtitle}>
                    Sign in to your PROTEC Alumni account
                  </ThemedText>
                </AnimatedView>
              )}

              {/* Tablet Header */}
              {isTablet && (
                <AnimatedView animation="fadeIn" delay={600} style={styles.tabletHeader}>
                  <ThemedText style={styles.tabletTitle}>Welcome Back</ThemedText>
                  <ThemedText style={styles.tabletSubtitle}>
                    Sign in to your account
                  </ThemedText>
                </AnimatedView>
              )}

              {/* Success Message */}
              <ScaleTransition visible={showSuccess}>
                <View
                  style={styles.successContainer}
                  {...getAccessibilityProps.alert('Magic link sent! Check your email.', 'success')}
                >
                  <ThemedText style={styles.successText}>
                    ✅ Magic link sent! Check your email.
                  </ThemedText>
                </View>
              </ScaleTransition>

              {/* Form */}
              <AnimatedView animation="fadeIn" delay={800} style={styles.form}>
                {/* Auth Method Toggle */}
                <View style={styles.toggleContainer}>
                  <TouchableOpacity
                    style={[styles.toggleButton, useCredentials && styles.toggleButtonActive]}
                    onPress={() => setUseCredentials(true)}
                    accessibilityLabel="Use password authentication"
                    accessibilityRole="button"
                  >
                    <ThemedText style={[styles.toggleText, useCredentials && styles.toggleTextActive]}>
                      Password
                    </ThemedText>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.toggleButton, !useCredentials && styles.toggleButtonActive]}
                    onPress={() => setUseCredentials(false)}
                    accessibilityLabel="Use magic link authentication"
                    accessibilityRole="button"
                  >
                    <ThemedText style={[styles.toggleText, !useCredentials && styles.toggleTextActive]}>
                      Magic Link
                    </ThemedText>
                  </TouchableOpacity>
                </View>

                <ShakeView trigger={!!emailError} intensity={5}>
                  <Input
                    label="Email Address"
                    value={email}
                    onChangeText={handleEmailChange}
                    placeholder="Enter your email"
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoCorrect={false}
                    error={emailError}
                    variant="outlined"
                    size="lg"
                  />
                </ShakeView>

                {useCredentials && (
                  <ShakeView trigger={!!passwordError} intensity={5}>
                    <Input
                      label="Password"
                      value={password}
                      onChangeText={handlePasswordChange}
                      placeholder="Enter your password"
                      secureTextEntry
                      error={passwordError}
                      variant="outlined"
                      size="lg"
                    />
                  </ShakeView>
                )}

                <Button
                  title={useCredentials ? "Sign In" : "Send Magic Link"}
                  onPress={useCredentials ? handleCredentialsLogin : handleEmailLogin}
                  disabled={isLoading || !email.trim() || (useCredentials && !password.trim())}
                  loading={isLoading}
                  variant="primary"
                  size="lg"
                  fullWidth
                  style={styles.primaryButton}
                  accessibilityLabel={useCredentials ? "Sign in with credentials" : "Send magic link to email"}
                  accessibilityHint={useCredentials ? "Signs you in with email and password" : "Sends a secure login link to your email address"}
                />

                <View style={styles.divider}>
                  <View style={styles.dividerLine} />
                  <ThemedText style={styles.dividerText}>or continue with</ThemedText>
                  <View style={styles.dividerLine} />
                </View>

                <View style={styles.socialButtons}>
                  <Button
                    title="Google"
                    onPress={handleGoogleLogin}
                    variant="outline"
                    size="lg"
                    style={styles.socialButton}
                    accessibilityLabel="Sign in with Google"
                    accessibilityHint="Opens Google sign in (coming soon)"
                  />

                  <Button
                    title="GitHub"
                    onPress={handleGitHubLogin}
                    variant="outline"
                    size="lg"
                    style={styles.socialButton}
                    accessibilityLabel="Sign in with GitHub"
                    accessibilityHint="Opens GitHub sign in (coming soon)"
                  />
                </View>
              </AnimatedView>

              {/* Footer */}
              <View style={styles.footer}>
                <ThemedText style={styles.footerText}>
                  Don&apos;t have an account?{' '}
                  <TouchableOpacity onPress={() => router.push('/auth/register')}>
                    <ThemedText style={styles.footerLink}>
                      Contact your alumni coordinator
                    </ThemedText>
                  </TouchableOpacity>
                </ThemedText>
              </View>
            </View>
          </AnimatedView>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: DesignSystem.Colors.light.background,
  },

  scrollContent: {
    flexGrow: 1,
    minHeight: height,
  },

  // Mobile Layout
  content: {
    flex: 1,
    paddingHorizontal: DesignSystem.Spacing['3xl'],
    paddingTop: DesignSystem.Spacing['6xl'],
    paddingBottom: DesignSystem.Spacing['4xl'],
    maxWidth: 400,
    alignSelf: 'center',
    width: '100%',
  },

  // Tablet Layout
  tabletContainer: {
    flex: 1,
    flexDirection: 'row',
    minHeight: height,
  },

  leftColumn: {
    flex: 1,
    padding: DesignSystem.Spacing['6xl'],
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },

  rightColumn: {
    flex: 1,
    padding: DesignSystem.Spacing['6xl'],
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },

  mobileColumn: {
    flex: 1,
    padding: DesignSystem.Spacing['3xl'],
    justifyContent: 'center',
  },

  // Form Card
  formCard: {
    backgroundColor: DesignSystem.Colors.light.background,
    borderRadius: DesignSystem.BorderRadius['2xl'],
    padding: DesignSystem.Spacing['4xl'],
    width: '100%',
    maxWidth: 450,
    ...DesignSystem.Shadows['2xl'],
  },

  // Branding Section (Tablet)
  brandingSection: {
    alignItems: 'center',
    maxWidth: 400,
  },

  brandTitle: {
    fontSize: 48,
    fontWeight: '900',
    color: DesignSystem.Colors.light.textInverse,
    textAlign: 'center',
    marginBottom: DesignSystem.Spacing['2xl'],
    letterSpacing: -2,
  },

  brandSubtitle: {
    fontSize: 20,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 28,
    marginBottom: DesignSystem.Spacing['4xl'],
  },

  featuresList: {
    alignItems: 'flex-start',
    width: '100%',
  },

  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: DesignSystem.Spacing['2xl'],
  },

  featureIcon: {
    fontSize: 24,
    marginRight: DesignSystem.Spacing.lg,
  },

  featureText: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: DesignSystem.Typography.fontWeight.medium,
  },

  // Headers
  mobileHeader: {
    alignItems: 'center',
    marginBottom: DesignSystem.Spacing['4xl'],
  },

  tabletHeader: {
    alignItems: 'center',
    marginBottom: DesignSystem.Spacing['3xl'],
  },

  mobileLogoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: DesignSystem.Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: DesignSystem.Spacing['2xl'],
    ...DesignSystem.Shadows.lg,
  },

  logoContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: DesignSystem.Spacing['3xl'],
    ...DesignSystem.Shadows.lg,
  },

  mobileLogo: {
    width: 50,
    height: 50,
  },

  logo: {
    width: 80,
    height: 80,
  },

  mobileTitle: {
    fontSize: 28,
    fontWeight: '800',
    textAlign: 'center',
    marginBottom: DesignSystem.Spacing.md,
    color: DesignSystem.Colors.light.text,
  },

  tabletTitle: {
    fontSize: 32,
    fontWeight: '800',
    textAlign: 'center',
    marginBottom: DesignSystem.Spacing.md,
    color: DesignSystem.Colors.light.text,
  },

  mobileSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: DesignSystem.Colors.light.textSecondary,
    lineHeight: 24,
  },

  tabletSubtitle: {
    fontSize: 18,
    textAlign: 'center',
    color: DesignSystem.Colors.light.textSecondary,
    lineHeight: 26,
  },

  // Form Elements
  successContainer: {
    backgroundColor: DesignSystem.Colors.light.success,
    paddingHorizontal: DesignSystem.Spacing.lg,
    paddingVertical: DesignSystem.Spacing.md,
    borderRadius: DesignSystem.BorderRadius.lg,
    marginBottom: DesignSystem.Spacing.lg,
    alignItems: 'center',
  },

  successText: {
    color: DesignSystem.Colors.light.textInverse,
    fontWeight: DesignSystem.Typography.fontWeight.medium,
    textAlign: 'center',
  },

  form: {
    width: '100%',
    marginBottom: DesignSystem.Spacing['2xl'],
  },

  toggleContainer: {
    flexDirection: 'row',
    backgroundColor: DesignSystem.Colors.light.backgroundSecondary,
    borderRadius: DesignSystem.BorderRadius.lg,
    padding: 4,
    marginBottom: DesignSystem.Spacing.lg,
  },

  toggleButton: {
    flex: 1,
    paddingVertical: DesignSystem.Spacing.md,
    paddingHorizontal: DesignSystem.Spacing.lg,
    borderRadius: DesignSystem.BorderRadius.md,
    alignItems: 'center',
  },

  toggleButtonActive: {
    backgroundColor: DesignSystem.Colors.light.background,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },

  toggleText: {
    fontSize: DesignSystem.Typography.fontSize.sm,
    fontWeight: DesignSystem.Typography.fontWeight.medium,
    color: DesignSystem.Colors.light.textMuted,
  },

  toggleTextActive: {
    color: DesignSystem.Colors.light.text,
  },

  primaryButton: {
    marginTop: DesignSystem.Spacing.lg,
    marginBottom: DesignSystem.Spacing['2xl'],
  },

  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: DesignSystem.Spacing['2xl'],
  },

  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: DesignSystem.Colors.light.border,
  },

  dividerText: {
    marginHorizontal: DesignSystem.Spacing.lg,
    fontSize: DesignSystem.Typography.fontSize.sm,
    color: DesignSystem.Colors.light.textMuted,
  },

  socialButtons: {
    flexDirection: 'row',
    gap: DesignSystem.Spacing.md,
  },

  socialButton: {
    flex: 1,
  },

  footer: {
    alignItems: 'center',
    marginTop: DesignSystem.Spacing.lg,
  },

  footerText: {
    fontSize: DesignSystem.Typography.fontSize.sm,
    color: DesignSystem.Colors.light.textMuted,
    textAlign: 'center',
    lineHeight: 20,
  },

  footerLink: {
    color: DesignSystem.Colors.light.primary,
    fontWeight: DesignSystem.Typography.fontWeight.medium,
  },
});
