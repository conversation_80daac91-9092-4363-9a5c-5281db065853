import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Alert } from 'react-native';
import { router } from 'expo-router';
import { 
  getAuthToken, 
  getUserData, 
  setAuthToken, 
  setUserData, 
  clearAllAuthData 
} from '../auth/storage';
import { MagicLinkService } from '../../services/magicLinkService';
import { AuthService } from '../../services/authService';

export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  isActive: boolean;
  photoUrl?: string;
  graduationYear: number;
  programmes: string[];
  currentRole?: string;
  company?: string;
  bio?: string;
  stats?: {
    connections: number;
    posts: number;
    donations: number;
    organizedEvents: number;
  };
}

export interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signInWithCredentials: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signUpWithCredentials: (data: { email: string; password: string; name: string }) => Promise<{ success: boolean; error?: string }>;
  signInWithMagicLink: (email: string) => Promise<{ success: boolean; error?: string }>;
  verifyMagicLink: (token: string, email: string) => Promise<{ success: boolean; error?: string }>;
  signInWithGoogle: () => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check authentication status on app start
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      setIsLoading(true);
      
      const [token, userData] = await Promise.all([
        getAuthToken(),
        getUserData()
      ]);

      if (token && userData) {
        // Verify token is still valid by making a test API call
        const isValid = await verifyTokenWithServer(token);
        
        if (isValid) {
          setUser(userData);
          setIsAuthenticated(true);
        } else {
          // Token is invalid, clear auth data
          await clearAllAuthData();
          setUser(null);
          setIsAuthenticated(false);
        }
      } else {
        setUser(null);
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const verifyTokenWithServer = async (token: string): Promise<boolean> => {
    try {
      // Make a simple API call to verify token
      const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL}/api/trpc/auth.me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      
      return response.ok;
    } catch (error) {
      console.error('Token verification failed:', error);
      return false;
    }
  };

  const signInWithCredentials = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL}/api/auth/credentials`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        return { success: false, error: data.message || 'Login failed' };
      }

      if (data.success && data.user && data.token) {
        await setAuthToken(data.token);
        await setUserData(data.user);
        setUser(data.user);
        setIsAuthenticated(true);

        // Navigate to main app
        router.replace('/(tabs)');

        return { success: true };
      } else {
        return { success: false, error: 'Invalid response from server' };
      }
    } catch (error) {
      console.error('Credentials sign in error:', error);
      return {
        success: false,
        error: 'Failed to sign in. Please check your connection and try again.'
      };
    }
  };

  const signUpWithCredentials = async (data: { email: string; password: string; name: string }): Promise<{ success: boolean; error?: string }> => {
    try {
      const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          graduationYear: new Date().getFullYear(), // Default for dev testing
          programmes: ['Development Testing'], // Default for dev testing
          province: 'Gauteng', // Default for dev testing
          city: 'Johannesburg', // Default for dev testing
          country: 'South Africa',
        }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        return { success: false, error: responseData.message || 'Registration failed' };
      }

      if (responseData.success && responseData.user && responseData.token) {
        await setAuthToken(responseData.token);
        await setUserData(responseData.user);
        setUser(responseData.user);
        setIsAuthenticated(true);

        // Navigate to main app
        router.replace('/(tabs)');

        return { success: true };
      } else {
        return { success: false, error: 'Invalid response from server' };
      }
    } catch (error) {
      console.error('Credentials sign up error:', error);
      return {
        success: false,
        error: 'Failed to sign up. Please check your connection and try again.'
      };
    }
  };

  const signInWithMagicLink = async (email: string): Promise<{ success: boolean; error?: string }> => {
    try {
      const result = await MagicLinkService.sendMagicLink({
        email: email.trim(),
        redirectUrl: '/(tabs)',
      });

      if (result.success) {
        return { success: true };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Magic link sign in error:', error);
      return { 
        success: false, 
        error: 'Failed to send magic link. Please try again.' 
      };
    }
  };

  const verifyMagicLink = async (token: string, email: string): Promise<{ success: boolean; error?: string }> => {
    try {
      const result = await MagicLinkService.verifyToken(token, email);

      if (result.success && result.user && result.token) {
        setUser(result.user);
        setIsAuthenticated(true);
        
        // Navigate to main app
        router.replace('/(tabs)');
        
        return { success: true };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Magic link verification error:', error);
      return { 
        success: false, 
        error: 'Failed to verify magic link. Please try again.' 
      };
    }
  };

  const signInWithGoogle = async (): Promise<{ success: boolean; error?: string }> => {
    try {
      const authUser = await AuthService.signInWithGoogle();
      
      if (authUser) {
        // Convert AuthUser to User format
        const userData: User = {
          id: authUser.id,
          email: authUser.email,
          name: authUser.name,
          role: 'ALUMNI', // Default role
          isActive: true,
          photoUrl: authUser.profilePicture,
          graduationYear: new Date().getFullYear(), // Will be updated during onboarding
          programmes: [],
          currentRole: undefined,
          company: undefined,
          bio: undefined,
        };

        await setAuthToken(authUser.accessToken);
        await setUserData(userData);
        
        setUser(userData);
        setIsAuthenticated(true);
        
        // Navigate to main app
        router.replace('/(tabs)');
        
        return { success: true };
      } else {
        return { success: false, error: 'Google sign in was cancelled or failed' };
      }
    } catch (error) {
      console.error('Google sign in error:', error);
      return { 
        success: false, 
        error: 'Failed to sign in with Google. Please try again.' 
      };
    }
  };

  const signOut = async (): Promise<void> => {
    try {
      setIsLoading(true);
      
      // Clear local auth data
      await clearAllAuthData();
      
      // Clear OAuth data if exists
      await AuthService.signOut();
      
      setUser(null);
      setIsAuthenticated(false);
      
      // Navigate to login screen
      router.replace('/auth/login');
    } catch (error) {
      console.error('Sign out error:', error);
      Alert.alert('Error', 'Failed to sign out. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshUser = async (): Promise<void> => {
    try {
      const token = await getAuthToken();
      if (!token) return;

      // Fetch updated user data from server
      const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL}/api/trpc/auth.me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const userData = await response.json();
        await setUserData(userData);
        setUser(userData);
      }
    } catch (error) {
      console.error('Error refreshing user data:', error);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    signInWithCredentials,
    signUpWithCredentials,
    signInWithMagicLink,
    verifyMagicLink,
    signInWithGoogle,
    signOut,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;
