import * as WebBrowser from 'expo-web-browser';
import * as Linking from 'expo-linking';
import { Alert } from 'react-native';
import { config } from '../config/env';
import { getAuthToken } from '../auth/storage';

export interface PaymentRequest {
  amount: number;
  currency: 'ZAR' | 'USD';
  gateway: 'payfast' | 'snapscan' | 'ozow' | 'paypal';
  purpose: string;
  frequency: 'ONE_TIME' | 'MONTHLY' | 'QUARTERLY' | 'ANNUALLY';
  donorEmail: string;
  donorName: string;
  donationId?: string;
  isAnonymous?: boolean;
  dedicationMessage?: string;
}

export interface PaymentResponse {
  success: boolean;
  paymentUrl?: string;
  paymentId?: string;
  error?: string;
  metadata?: any;
}

export interface PaymentStatus {
  id: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  amount: number;
  currency: string;
  gateway: string;
  createdAt: Date;
  completedAt?: Date;
  metadata?: any;
}

export class PaymentService {
  private static readonly BASE_URL = config.API_URL || 'https://protec-alumni.vercel.app';

  /**
   * Create a payment request
   */
  static async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      const token = await getAuthToken();
      
      const response = await fetch(`${this.BASE_URL}/api/payments/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          'User-Agent': 'PROTEC Alumni Mobile App',
        },
        body: JSON.stringify({
          ...request,
          returnUrl: Linking.createURL('/donations/success'),
          cancelUrl: Linking.createURL('/donations/cancel'),
          notifyUrl: `${this.BASE_URL}/api/webhooks/${request.gateway}`,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          error: errorData.error || 'Failed to create payment',
        };
      }

      const result = await response.json();
      return {
        success: true,
        paymentUrl: result.paymentUrl,
        paymentId: result.paymentId,
        metadata: result.metadata,
      };
    } catch (error) {
      console.error('Payment creation error:', error);
      return {
        success: false,
        error: 'Network error. Please check your connection and try again.',
      };
    }
  }

  /**
   * Process payment with PayFast
   */
  static async processPayFastPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      const paymentResponse = await this.createPayment({
        ...request,
        gateway: 'payfast',
      });

      if (!paymentResponse.success || !paymentResponse.paymentUrl) {
        return paymentResponse;
      }

      // Open PayFast payment page
      const result = await WebBrowser.openBrowserAsync(paymentResponse.paymentUrl, {
        presentationStyle: WebBrowser.WebBrowserPresentationStyle.FORM_SHEET,
        controlsColor: '#012A5B',
        toolbarColor: '#FFFFFF',
      });

      // Handle the result based on how the browser was closed
      if (result.type === 'cancel') {
        return {
          success: false,
          error: 'Payment was cancelled',
        };
      }

      // Return success - actual payment verification will happen via webhooks
      return {
        success: true,
        paymentId: paymentResponse.paymentId,
        metadata: { browserResult: result },
      };
    } catch (error) {
      console.error('PayFast payment error:', error);
      return {
        success: false,
        error: 'Failed to process PayFast payment',
      };
    }
  }

  /**
   * Process payment with SnapScan
   */
  static async processSnapScanPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      const paymentResponse = await this.createPayment({
        ...request,
        gateway: 'snapscan',
      });

      if (!paymentResponse.success || !paymentResponse.paymentUrl) {
        return paymentResponse;
      }

      // Open SnapScan payment page
      const result = await WebBrowser.openBrowserAsync(paymentResponse.paymentUrl, {
        presentationStyle: WebBrowser.WebBrowserPresentationStyle.FORM_SHEET,
        controlsColor: '#00A651',
        toolbarColor: '#FFFFFF',
      });

      if (result.type === 'cancel') {
        return {
          success: false,
          error: 'Payment was cancelled',
        };
      }

      return {
        success: true,
        paymentId: paymentResponse.paymentId,
        metadata: { browserResult: result },
      };
    } catch (error) {
      console.error('SnapScan payment error:', error);
      return {
        success: false,
        error: 'Failed to process SnapScan payment',
      };
    }
  }

  /**
   * Process payment with Ozow
   */
  static async processOzowPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      const paymentResponse = await this.createPayment({
        ...request,
        gateway: 'ozow',
      });

      if (!paymentResponse.success || !paymentResponse.paymentUrl) {
        return paymentResponse;
      }

      // Open Ozow payment page
      const result = await WebBrowser.openBrowserAsync(paymentResponse.paymentUrl, {
        presentationStyle: WebBrowser.WebBrowserPresentationStyle.FORM_SHEET,
        controlsColor: '#FF6B35',
        toolbarColor: '#FFFFFF',
      });

      if (result.type === 'cancel') {
        return {
          success: false,
          error: 'Payment was cancelled',
        };
      }

      return {
        success: true,
        paymentId: paymentResponse.paymentId,
        metadata: { browserResult: result },
      };
    } catch (error) {
      console.error('Ozow payment error:', error);
      return {
        success: false,
        error: 'Failed to process Ozow payment',
      };
    }
  }

  /**
   * Process payment with PayPal
   */
  static async processPayPalPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      const paymentResponse = await this.createPayment({
        ...request,
        gateway: 'paypal',
        currency: 'USD', // PayPal typically uses USD
      });

      if (!paymentResponse.success || !paymentResponse.paymentUrl) {
        return paymentResponse;
      }

      // Open PayPal payment page
      const result = await WebBrowser.openBrowserAsync(paymentResponse.paymentUrl, {
        presentationStyle: WebBrowser.WebBrowserPresentationStyle.FORM_SHEET,
        controlsColor: '#0070BA',
        toolbarColor: '#FFFFFF',
      });

      if (result.type === 'cancel') {
        return {
          success: false,
          error: 'Payment was cancelled',
        };
      }

      return {
        success: true,
        paymentId: paymentResponse.paymentId,
        metadata: { browserResult: result },
      };
    } catch (error) {
      console.error('PayPal payment error:', error);
      return {
        success: false,
        error: 'Failed to process PayPal payment',
      };
    }
  }

  /**
   * Get payment status
   */
  static async getPaymentStatus(paymentId: string): Promise<PaymentStatus | null> {
    try {
      const token = await getAuthToken();
      
      const response = await fetch(`${this.BASE_URL}/api/payments/${paymentId}/status`, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
        },
      });

      if (!response.ok) {
        return null;
      }

      const result = await response.json();
      return {
        ...result,
        createdAt: new Date(result.createdAt),
        completedAt: result.completedAt ? new Date(result.completedAt) : undefined,
      };
    } catch (error) {
      console.error('Error getting payment status:', error);
      return null;
    }
  }

  /**
   * Show payment gateway selection
   */
  static showPaymentGatewaySelection(
    amount: number,
    currency: 'ZAR' | 'USD',
    onSelect: (gateway: string) => void
  ) {
    const gateways = currency === 'ZAR'
      ? [
          { name: 'PayFast', description: 'Credit/Debit Cards, EFT', fees: '2.9% + R2', available: true },
          { name: 'SnapScan', description: 'QR Code Payment', fees: '2.9%', available: true },
          { name: 'Ozow', description: 'Instant EFT', fees: '1.5%', available: true },
        ]
      : [
          { name: 'PayPal', description: 'International Payments', fees: '3.4% + $0.30', available: true },
        ];

    const buttons = gateways
      .filter(gateway => gateway.available)
      .map(gateway => ({
        text: `${gateway.name} (${gateway.fees})`,
        onPress: () => onSelect(gateway.name.toLowerCase()),
      }));

    buttons.push({ text: 'Cancel', onPress: () => {}, style: 'cancel' as const });

    Alert.alert(
      'Select Payment Method',
      `Choose how you'd like to pay ${currency} ${amount.toFixed(2)}`,
      buttons
    );
  }
}

export default PaymentService;
