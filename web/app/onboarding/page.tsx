"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRout<PERSON> } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { OnboardingWelcome } from "@/components/onboarding/onboarding-welcome"
import { PersonalInfoStep } from "@/components/onboarding/personal-info-step"
import { ProtecBackgroundStep } from "@/components/onboarding/protec-background-step"
import { ProfessionalInfoStep } from "@/components/onboarding/professional-info-step"
import { PreferencesStep } from "@/components/onboarding/preferences-step"
import { OnboardingComplete } from "@/components/onboarding/onboarding-complete"
import { api } from "@/components/providers/trpc-provider"
import { toast } from "sonner"
import { 
  CheckCircle, 
  User, 
  GraduationCap, 
  Briefcase, 
  Settings,
  X
} from "lucide-react"

const ONBOARDING_STEPS = [
  {
    id: 'welcome',
    title: 'Welcome',
    description: 'Welcome to PROTEC Alumni',
    icon: CheckCircle,
  },
  {
    id: 'personal',
    title: 'Personal Info',
    description: 'Tell us about yourself',
    icon: User,
  },
  {
    id: 'protec',
    title: 'PROTEC Background',
    description: 'Your PROTEC journey',
    icon: GraduationCap,
  },
  {
    id: 'professional',
    title: 'Professional Info',
    description: 'Your career and skills',
    icon: Briefcase,
  },
  {
    id: 'preferences',
    title: 'Preferences',
    description: 'Privacy and notifications',
    icon: Settings,
  },
  {
    id: 'complete',
    title: 'Complete',
    description: 'You\'re all set!',
    icon: CheckCircle,
  },
]

export default function OnboardingPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(0)
  const [isSkipping, setIsSkipping] = useState(false)

  const { data: onboardingData, isLoading, refetch } = api.onboarding.getStatus.useQuery(
    undefined,
    { enabled: !!session }
  )

  const skipOnboardingMutation = api.onboarding.skipOnboarding.useMutation({
    onSuccess: () => {
      toast.success("Welcome to PROTEC Alumni!")
      router.push("/dashboard")
    },
    onError: (error) => {
      toast.error(error.message || "Failed to skip onboarding")
      setIsSkipping(false)
    },
  })

  const completeOnboardingMutation = api.onboarding.completeOnboarding.useMutation({
    onSuccess: () => {
      toast.success("Welcome to PROTEC Alumni!")
      router.push("/dashboard")
    },
    onError: (error) => {
      toast.error(error.message || "Failed to complete onboarding")
    },
  })

  // Redirect if not authenticated
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin")
    }
  }, [status, router])

  // Set current step based on onboarding status
  useEffect(() => {
    if (onboardingData?.onboardingStatus) {
      const { isComplete, currentStep: statusStep } = onboardingData.onboardingStatus
      if (isComplete) {
        setCurrentStep(5) // Complete step
      } else {
        setCurrentStep(statusStep) // Current step from API
      }
    }
  }, [onboardingData])

  const handleNext = () => {
    if (currentStep < ONBOARDING_STEPS.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSkip = async () => {
    setIsSkipping(true)
    await skipOnboardingMutation.mutateAsync()
  }

  const handleComplete = async () => {
    await completeOnboardingMutation.mutateAsync()
  }

  const handleStepComplete = () => {
    refetch()
    handleNext()
  }

  if (status === "loading" || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5 flex items-center justify-center p-4">
        <Card className="w-full max-w-4xl">
          <CardContent className="p-8">
            <div className="space-y-6">
              <div className="text-center space-y-4">
                <Skeleton className="h-12 w-48 mx-auto" />
                <Skeleton className="h-4 w-64 mx-auto" />
              </div>
              <Skeleton className="h-2 w-full" />
              <div className="space-y-4">
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-32 w-full" />
                <div className="flex justify-between">
                  <Skeleton className="h-10 w-24" />
                  <Skeleton className="h-10 w-24" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (status === "unauthenticated") {
    return null
  }

  //const currentStepData = ONBOARDING_STEPS[currentStep]
  const progress = ((currentStep) / (ONBOARDING_STEPS.length - 1)) * 100

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-2">
            <div className="flex h-10 w-10 items-center justify-center rounded-md bg-primary">
              <span className="text-lg font-bold text-primary-foreground">P</span>
            </div>
            <span className="text-2xl font-bold text-primary">PROTEC Alumni</span>
          </div>
          
          {currentStep > 0 && currentStep < ONBOARDING_STEPS.length - 1 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <span>Step {currentStep} of {ONBOARDING_STEPS.length - 2}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSkip}
                  disabled={isSkipping}
                  className="text-muted-foreground hover:text-foreground"
                >
                  {isSkipping ? (
                    <>
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current mr-2" />
                      Skipping...
                    </>
                  ) : (
                    <>
                      <X className="mr-1 h-3 w-3" />
                      Skip for now
                    </>
                  )}
                </Button>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}
        </div>

        {/* Main Content */}
        <Card className="shadow-lg border-2">
          <CardContent className="p-8">
            {currentStep === 0 && (
              <OnboardingWelcome
                alumni={onboardingData?.alumni}
                onNext={handleNext}
                onSkip={handleSkip}
                isSkipping={isSkipping}
              />
            )}

            {currentStep === 1 && (
              <PersonalInfoStep
                alumni={onboardingData?.alumni}
                onNext={handleStepComplete}
                onPrevious={handlePrevious}
              />
            )}

            {currentStep === 2 && (
              <ProtecBackgroundStep
                alumni={onboardingData?.alumni}
                onNext={handleStepComplete}
                onPrevious={handlePrevious}
              />
            )}

            {currentStep === 3 && (
              <ProfessionalInfoStep
                alumni={onboardingData?.alumni}
                onNext={handleStepComplete}
                onPrevious={handlePrevious}
              />
            )}

            {currentStep === 4 && (
              <PreferencesStep
                alumni={onboardingData?.alumni}
                onNext={handleStepComplete}
                onPrevious={handlePrevious}
              />
            )}

            {currentStep === 5 && (
              <OnboardingComplete
                alumni={onboardingData?.alumni}
                onComplete={handleComplete}
                isCompleting={completeOnboardingMutation.isPending}
              />
            )}
          </CardContent>
        </Card>

        {/* Step Indicator */}
        {currentStep > 0 && currentStep < ONBOARDING_STEPS.length - 1 && (
          <div className="flex justify-center">
            <div className="flex space-x-2">
              {ONBOARDING_STEPS.slice(1, -1).map((step, index) => {
                const stepIndex = index + 1
                const isActive = stepIndex === currentStep
                const isCompleted = stepIndex < currentStep
                const Icon = step.icon

                return (
                  <div
                    key={step.id}
                    className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors ${
                      isCompleted
                        ? 'bg-primary border-primary text-primary-foreground'
                        : isActive
                        ? 'border-primary text-primary bg-primary/10'
                        : 'border-muted-foreground/30 text-muted-foreground'
                    }`}
                  >
                    {isCompleted ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                )
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
