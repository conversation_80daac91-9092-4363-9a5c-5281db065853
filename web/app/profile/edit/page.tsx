"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { useRouter } from "next/navigation"
import { MainNav } from "@/components/navigation/main-nav"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { api } from "@/components/providers/trpc-provider"
import { 
  User, 
  Briefcase, 
  GraduationCap, 
  Heart,
  Settings,
  ArrowLeft,
  Save,
  Loader2
} from "lucide-react"
import { toast } from "sonner"

export default function ProfileEditPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("personal")
  const [isSaving, setIsSaving] = useState(false)

  // Get complete profile data
  const { data: profile, isLoading, refetch } = api.profile.getComplete.useQuery()

  if (status === "loading" || isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-protec-red"></div>
      </div>
    )
  }

  if (status === "unauthenticated") {
    redirect("/auth/signin")
  }

  if (!profile) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center space-y-4">
          <h2 className="text-2xl font-bold text-gray-900">Profile Not Found</h2>
          <p className="text-gray-600">Unable to load your profile information.</p>
          <Button onClick={() => router.push("/profile")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Profile
          </Button>
        </div>
      </div>
    )
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      await refetch()
      toast.success("Profile updated successfully!")
      router.push("/profile")
    } catch (error) {
      toast.error("Failed to save profile changes")
    } finally {
      setIsSaving(false)
    }
  }

  const tabs = [
    {
      id: "personal",
      label: "Personal Info",
      icon: User,
      description: "Basic information and contact details"
    },
    {
      id: "professional",
      label: "Professional",
      icon: Briefcase,
      description: "Career history and current role"
    },
    {
      id: "education",
      label: "Education",
      icon: GraduationCap,
      description: "Educational background and qualifications"
    },
    {
      id: "protec",
      label: "PROTEC Involvement",
      icon: Heart,
      description: "Your involvement with PROTEC programs"
    },
    {
      id: "preferences",
      label: "Preferences",
      icon: Settings,
      description: "Privacy settings and preferences"
    }
  ]

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1 p-6">
        <div className="max-w-6xl mx-auto space-y-6">
          {/* Header */}
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push("/profile")}
                className="shrink-0"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Profile
              </Button>
              <div>
                <h1 className="text-3xl font-bold tracking-tight text-protec-navy">
                  Edit Profile
                </h1>
                <p className="text-muted-foreground">
                  Update your information to help fellow alumni connect with you
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Badge variant="outline" className="border-green-200 bg-green-50 text-green-700">
                <User className="mr-1 h-3 w-3" />
                {profile.role}
              </Badge>
              <Button 
                onClick={handleSave}
                disabled={isSaving}
                className="bg-protec-red hover:bg-protec-red/90"
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Profile Edit Tabs */}
          <Card className="shadow-lg border-2">
            <CardContent className="p-0">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <div className="border-b bg-muted/30 p-6">
                  <TabsList className="grid w-full grid-cols-5 h-auto p-1">
                    {tabs.map((tab) => {
                      const Icon = tab.icon
                      return (
                        <TabsTrigger
                          key={tab.id}
                          value={tab.id}
                          className="flex flex-col items-center space-y-2 p-4 data-[state=active]:bg-white data-[state=active]:shadow-sm"
                        >
                          <Icon className="h-5 w-5" />
                          <div className="text-center">
                            <div className="font-medium text-sm">{tab.label}</div>
                            <div className="text-xs text-muted-foreground hidden md:block">
                              {tab.description}
                            </div>
                          </div>
                        </TabsTrigger>
                      )
                    })}
                  </TabsList>
                </div>

                <div className="p-6">
                  <TabsContent value="personal" className="mt-0">
                    <ProfileEditForm 
                      profile={profile} 
                      section="personal"
                      onSave={refetch}
                    />
                  </TabsContent>

                  <TabsContent value="professional" className="mt-0">
                    <ProfileEditForm 
                      profile={profile} 
                      section="professional"
                      onSave={refetch}
                    />
                  </TabsContent>

                  <TabsContent value="education" className="mt-0">
                    <ProfileEditForm 
                      profile={profile} 
                      section="education"
                      onSave={refetch}
                    />
                  </TabsContent>

                  <TabsContent value="protec" className="mt-0">
                    <ProfileEditForm 
                      profile={profile} 
                      section="protec"
                      onSave={refetch}
                    />
                  </TabsContent>

                  <TabsContent value="preferences" className="mt-0">
                    <ProfileEditForm 
                      profile={profile} 
                      section="preferences"
                      onSave={refetch}
                    />
                  </TabsContent>
                </div>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
