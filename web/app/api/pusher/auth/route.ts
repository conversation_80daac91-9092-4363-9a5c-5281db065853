import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { authenticateUser, authenticatePresence } from '@/lib/pusher'

export async function POST(req: NextRequest) {
  try {
    // Get the session
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user from database
    const user = await prisma.alumni.findUnique({
      where: { email: session.user.email },
      select: { id: true, name: true, email: true, photoUrl: true }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Parse the request body
    const body = await req.text()
    const params = new URLSearchParams(body)
    const socketId = params.get('socket_id')
    const channelName = params.get('channel_name')

    if (!socketId || !channelName) {
      return NextResponse.json({ error: 'Missing socket_id or channel_name' }, { status: 400 })
    }

    // Handle different channel types
    if (channelName.startsWith('private-conversation-')) {
      // Extract conversation ID from channel name
      const conversationId = channelName.replace('private-conversation-', '')
      
      // Check if user is a participant in this conversation
      const participant = await prisma.conversationParticipant.findFirst({
        where: {
          conversationId,
          alumniId: user.id,
          isActive: true
        }
      })

      if (!participant) {
        return NextResponse.json({ error: 'Not authorized for this conversation' }, { status: 403 })
      }

      // Authenticate the private channel
      const auth = authenticateUser(socketId, channelName, user)
      return NextResponse.json(auth)
    }
    
    else if (channelName.startsWith('private-user-')) {
      // Extract user ID from channel name
      const channelUserId = channelName.replace('private-user-', '')
      
      // Users can only subscribe to their own user channel
      if (channelUserId !== user.id) {
        return NextResponse.json({ error: 'Not authorized for this user channel' }, { status: 403 })
      }

      // Authenticate the private channel
      const auth = authenticateUser(socketId, channelName, user)
      return NextResponse.json(auth)
    }
    
    else if (channelName.startsWith('presence-')) {
      // Handle presence channels (for online status)
      if (channelName === 'presence-alumni-online') {
        // All authenticated users can join the general presence channel
        const auth = authenticatePresence(socketId, channelName, user)
        return NextResponse.json(auth)
      }
      
      // For other presence channels, add specific authorization logic
      return NextResponse.json({ error: 'Not authorized for this presence channel' }, { status: 403 })
    }
    
    else {
      // Unknown channel type
      return NextResponse.json({ error: 'Unknown channel type' }, { status: 400 })
    }

  } catch (error) {
    console.error('Pusher auth error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(req: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
