import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { emailService } from '@/lib/services/email-service'

const registerSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters long')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
  name: z.string().min(1, 'Name is required'),
  graduationYear: z.number().min(1980).max(new Date().getFullYear() + 10),
  programmes: z.array(z.string()).min(1, 'At least one programme is required'),
  province: z.string().min(1, 'Province is required'),
  city: z.string().min(1, 'City is required'),
  country: z.string().default('South Africa'),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validationResult = registerSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false,
          message: 'Invalid input',
          errors: validationResult.error.errors 
        },
        { status: 400 }
      )
    }

    const { email, password, name, graduationYear, programmes, province, city, country } = validationResult.data

    // Check if user already exists
    const existingAlumni = await prisma.alumni.findUnique({
      where: { email: email.toLowerCase() }
    })

    if (existingAlumni) {
      return NextResponse.json(
        { 
          success: false,
          message: 'An account with this email already exists' 
        },
        { status: 409 }
      )
    }

    // Hash password
    const saltRounds = 12
    const hashedPassword = await bcrypt.hash(password, saltRounds)

    // Create new alumni record
    const alumni = await prisma.alumni.create({
      data: {
        email: email.toLowerCase(),
        name,
        password: hashedPassword,
        graduationYear,
        programmes,
        province,
        city,
        country,
        skills: [],
        interests: [],
        role: 'ALUMNI',
        isActive: true,
        privacy: {
          showEmail: false,
          showPhone: false,
          showLocation: true,
          showConnections: true,
        },
      },
      select: {
        id: true,
        email: true,
        name: true,
        photoUrl: true,
        role: true,
        isActive: true,
        province: true,
        city: true,
        country: true,
        graduationYear: true,
        programmes: true,
      }
    })

    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: alumni.id,
        email: alumni.email,
        role: alumni.role 
      },
      process.env.NEXTAUTH_SECRET!,
      { expiresIn: '7d' }
    )

    // Send welcome email (don't fail registration if email fails)
    try {
      await emailService.sendWelcomeEmail(alumni.email, alumni.name)
    } catch (emailError) {
      console.error('Failed to send welcome email:', emailError)
    }

    // Return user data and token
    const user = {
      id: alumni.id,
      email: alumni.email,
      name: alumni.name,
      image: alumni.photoUrl,
      role: alumni.role,
      isActive: alumni.isActive,
      province: alumni.province,
      city: alumni.city,
      country: alumni.country,
      graduationYear: alumni.graduationYear,
      programmes: alumni.programmes,
    }

    return NextResponse.json({
      success: true,
      user,
      token,
    })

  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { 
        success: false,
        message: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}
