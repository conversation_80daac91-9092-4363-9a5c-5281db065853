import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { triggerPusherEvent } from '@/lib/pusher'
import { z } from 'zod'

const sendMessageSchema = z.object({
  conversationId: z.string().uuid(),
  content: z.string().min(1).max(5000),
  messageType: z.enum(['text', 'image', 'file']).default('text'),
})

export async function POST(req: NextRequest) {
  try {
    // Get the session
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user from database
    const user = await prisma.alumni.findUnique({
      where: { email: session.user.email },
      select: { id: true, name: true, email: true, photoUrl: true }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Parse and validate request body
    const body = await req.json()
    const { conversationId, content, messageType } = sendMessageSchema.parse(body)

    // Check if user is a participant in this conversation
    const participant = await prisma.conversationParticipant.findFirst({
      where: {
        conversationId,
        alumniId: user.id,
        isActive: true
      }
    })

    if (!participant) {
      return NextResponse.json({ error: 'Not authorized to send message to this conversation' }, { status: 403 })
    }

    // Create the message in database
    const message = await prisma.message.create({
      data: {
        conversationId,
        senderId: user.id,
        content,
        messageType
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            photoUrl: true
          }
        }
      }
    })

    // Update conversation last message time
    await prisma.conversation.update({
      where: { id: conversationId },
      data: { 
        lastMessageAt: new Date(),
        lastMessage: content.substring(0, 100) // Store first 100 chars as preview
      }
    })

    // Trigger Pusher event to notify all participants
    await triggerPusherEvent(
      `private-conversation-${conversationId}`,
      'new-message',
      {
        id: message.id,
        conversationId: message.conversationId,
        senderId: message.senderId,
        content: message.content,
        messageType: message.messageType,
        createdAt: message.createdAt.toISOString(),
        sender: message.sender
      }
    )

    // Also trigger conversation update for participants' conversation lists
    const participants = await prisma.conversationParticipant.findMany({
      where: {
        conversationId,
        isActive: true,
        alumniId: { not: user.id } // Don't notify the sender
      },
      select: { alumniId: true }
    })

    // Notify each participant about the conversation update
    for (const participant of participants) {
      await triggerPusherEvent(
        `private-user-${participant.alumniId}`,
        'conversation-updated',
        {
          conversationId,
          type: 'new_message',
          lastMessage: content.substring(0, 100),
          lastMessageAt: message.createdAt.toISOString(),
          sender: message.sender
        }
      )
    }

    return NextResponse.json({
      success: true,
      message: {
        id: message.id,
        conversationId: message.conversationId,
        senderId: message.senderId,
        content: message.content,
        messageType: message.messageType,
        createdAt: message.createdAt.toISOString(),
        sender: message.sender
      }
    })

  } catch (error) {
    console.error('Send message error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request data', details: error.errors }, { status: 400 })
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(req: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
