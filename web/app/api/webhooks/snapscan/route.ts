import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { paymentService } from '@/lib/services/payment-service'

export async function POST(request: NextRequest) {
  try {
    // Get the raw body for signature verification
    const body = await request.text()
    
    // Parse the payload - SnapScan sends data as application/x-www-form-urlencoded
    const payload = new URLSearchParams(body)
    const payloadData = payload.get('payload')
    
    if (!payloadData) {
      console.error('SnapScan webhook: No payload data received')
      return NextResponse.json(
        { error: 'No payload data' },
        { status: 400 }
      )
    }

    // Parse the JSON payload
    const data = JSON.parse(payloadData)
    console.log('SnapScan webhook received:', data)

    // Verify the webhook signature
    const headers = Object.fromEntries(request.headers.entries())
    const verification = await paymentService.verifyWebhook('snapscan', body, headers)
    
    if (!verification.isValid) {
      console.error('SnapScan webhook verification failed:', verification.error)
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      )
    }

    // Extract payment information from SnapScan payload
    const {
      id: snapScanPaymentId,
      status,
      totalAmount,
      merchantReference,
      date,
      userReference,
      authCode
    } = data

    // Find the donation by merchant reference
    const donationId = merchantReference?.replace('PROTEC_', '')
    if (!donationId) {
      console.error('SnapScan webhook: Invalid merchant reference:', merchantReference)
      return NextResponse.json(
        { error: 'Invalid merchant reference' },
        { status: 400 }
      )
    }

    const donation = await prisma.donation.findUnique({
      where: { id: donationId },
      include: { alumni: true }
    })

    if (!donation) {
      console.error('SnapScan webhook: Donation not found:', donationId)
      return NextResponse.json(
        { error: 'Donation not found' },
        { status: 404 }
      )
    }

    // Map SnapScan status to our status
    let newStatus: string
    switch (status) {
      case 'completed':
        newStatus = 'completed'
        break
      case 'error':
        newStatus = 'failed'
        break
      case 'pending':
        newStatus = 'pending'
        break
      default:
        newStatus = 'pending'
    }

    // Update donation status
    const updatedDonation = await prisma.donation.update({
      where: { id: donationId },
      data: {
        status: newStatus,
        transactionId: snapScanPaymentId?.toString(),
        completedAt: status === 'completed' ? new Date(date) : null,
        metadata: {
          ...donation.metadata,
          snapScanPaymentId,
          authCode,
          userReference,
          totalAmountCents: totalAmount,
          webhookReceived: new Date().toISOString()
        }
      }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: newStatus === 'completed' ? 'DONATION_COMPLETED' : 'DONATION_UPDATED',
        refId: donationId,
        alumniId: donation.alumniId,
        metadata: {
          status,
          totalAmount,
          gateway: 'snapscan',
          snapScanPaymentId,
          authCode
        }
      }
    })

    // If payment is complete and it's a recurring donation, log a note
    // (SnapScan doesn't support recurring payments, so this is just for tracking)
    if (newStatus === 'completed' && donation.isRecurring) {
      console.log('SnapScan recurring donation completed (manual setup required):', {
        donationId,
        frequency: donation.frequency
      })
    }

    console.log('SnapScan webhook processed successfully:', {
      donationId,
      status: newStatus,
      totalAmount,
      snapScanPaymentId
    })

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('SnapScan webhook error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(req: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
