# SnapScan Payment Gateway Integration

This document outlines the SnapScan payment gateway integration for the PROTEC Alumni platform.

## Overview

SnapScan is a popular QR code-based payment system in South Africa that allows users to make payments by scanning QR codes with their mobile banking apps or the SnapScan app.

## Features

- ✅ QR Code payment generation
- ✅ Webhook payment notifications
- ✅ Payment verification and security
- ✅ Real-time payment status updates
- ⚠️ One-time payments only (no recurring payments)
- ✅ ZAR currency support

## Configuration

### Environment Variables

Add the following to your `.env.local` file:

```env
# SnapScan Configuration
SNAPSCAN_API_KEY="your-snapscan-api-key"
SNAPSCAN_SNAP_CODE="your-snapscan-snap-code"
SNAPSCAN_WEBHOOK_AUTH_KEY="your-snapscan-webhook-auth-key"
```

### Getting SnapScan Credentials

1. **Sign up for SnapScan Merchant Account**
   - Visit [SnapScan Merchant Portal](https://merchant.snapscan.co.za/)
   - Complete merchant registration
   - Get approved for payment processing

2. **API Key**
   - Available in your SnapScan merchant dashboard
   - Used for API authentication

3. **SnapCode**
   - Your unique merchant identifier
   - Displayed on your QR code stand
   - Used to generate payment URLs

4. **Webhook Auth Key**
   - Used to verify webhook authenticity
   - Contact SnapScan support to set up webhooks

## How It Works

### Payment Flow

1. **Payment Initiation**
   - User selects SnapScan as payment method
   - System generates unique merchant reference
   - QR code URL is created with amount and reference

2. **QR Code Display**
   - User is redirected to SnapScan payment page
   - QR code is displayed for scanning
   - User scans with banking app or SnapScan app

3. **Payment Processing**
   - User completes payment on their device
   - SnapScan processes the payment
   - Webhook notification sent to our system

4. **Payment Confirmation**
   - Webhook updates donation status
   - User receives confirmation
   - Activity log is updated

### QR Code Generation

SnapScan payments use QR codes with the following URL structure:

```
https://pos.snapscan.io/qr/{snapCode}?id={merchantReference}&amount={amountInCents}&strict=true
```

Parameters:
- `snapCode`: Your unique merchant identifier
- `id`: Unique reference for the payment (format: `PROTEC_{donationId}`)
- `amount`: Payment amount in cents
- `strict=true`: Prevents duplicate payments and amount changes

## API Integration

### Payment Creation

```typescript
const paymentRequest = {
  amount: 100.00,
  currency: 'ZAR',
  gateway: 'snapscan',
  purpose: 'general',
  frequency: 'ONE_TIME',
  donorEmail: '<EMAIL>',
  donorName: 'John Doe',
  returnUrl: 'https://app.protec.co.za/donations/success',
  cancelUrl: 'https://app.protec.co.za/donations/cancel',
  notifyUrl: 'https://app.protec.co.za/api/webhooks/snapscan',
  donationId: 'unique-donation-id'
}

const response = await paymentService.createPayment(paymentRequest)
```

### Webhook Handling

Webhooks are received at `/api/webhooks/snapscan` and include:

```json
{
  "id": 12345,
  "status": "completed",
  "date": "2024-01-15T10:30:00Z",
  "totalAmount": 10000,
  "merchantReference": "PROTEC_donation-id",
  "userReference": "John Doe",
  "authCode": "123456"
}
```

## Security

### Webhook Verification

All webhooks are verified using HMAC-SHA256 signatures:

1. SnapScan sends signature in `Authorization` header
2. Format: `SnapScan signature={hash}`
3. Hash is computed using webhook auth key
4. Constant-time comparison prevents timing attacks

### Payment Security

- Unique merchant references prevent duplicate payments
- `strict=true` parameter enforces exact amount matching
- Webhook verification ensures authentic notifications
- All payments are logged with full audit trail

## Limitations

### Recurring Payments

SnapScan does not support automatic recurring payments. For recurring donations:

1. Initial payment is processed normally
2. System logs the recurring intent
3. Manual follow-up required for subsequent payments
4. Consider alternative gateways for true recurring payments

### Currency Support

- Only ZAR (South African Rand) is supported
- International donors should use PayPal

## Testing

### Sandbox Environment

SnapScan uses the same URLs for sandbox and production. Testing is done with:

1. Test SnapCode provided by SnapScan
2. Small amount payments (R1-R5)
3. Webhook testing with ngrok or similar tools

### Test Scenarios

1. **Successful Payment**
   - Generate QR code
   - Scan and pay with test account
   - Verify webhook received
   - Check donation status updated

2. **Failed Payment**
   - Insufficient funds scenario
   - Verify error handling
   - Check status remains pending

3. **Webhook Verification**
   - Test with valid signature
   - Test with invalid signature
   - Verify security measures

## Monitoring

### Payment Tracking

Monitor SnapScan payments through:

1. **SnapScan Merchant Dashboard**
   - Real-time payment status
   - Transaction history
   - Settlement reports

2. **Application Logs**
   - Webhook processing logs
   - Payment creation logs
   - Error tracking

3. **Database Queries**
   - Donation status tracking
   - Payment reconciliation
   - Activity audit trails

## Troubleshooting

### Common Issues

1. **QR Code Not Working**
   - Check SnapCode configuration
   - Verify amount format (cents)
   - Ensure merchant reference is unique

2. **Webhook Not Received**
   - Check webhook URL configuration
   - Verify firewall/security settings
   - Test webhook endpoint manually

3. **Signature Verification Failed**
   - Check webhook auth key
   - Verify payload format
   - Review signature calculation

### Support

For SnapScan-specific issues:
- Email: <EMAIL>
- Documentation: https://developer.snapscan.co.za/
- Merchant Portal: https://merchant.snapscan.co.za/

## Production Checklist

- [ ] SnapScan merchant account approved
- [ ] API credentials configured
- [ ] Webhook endpoint tested
- [ ] SSL certificate valid
- [ ] Error handling implemented
- [ ] Monitoring set up
- [ ] Documentation updated
- [ ] Team training completed
