# Database Optimization Guide

## Overview

This document outlines the database optimization strategies implemented for the PROTEC Alumni platform, including indexing, query optimization, and performance monitoring.

## Database Indexes

### Alumni Table Indexes

```sql
-- Search and filtering indexes
CREATE INDEX idx_alumni_graduation_year ON Alumni(graduationYear);
CREATE INDEX idx_alumni_location ON Alumni(province, city);
CREATE INDEX idx_alumni_industry ON Alumni(industry);
CREATE INDEX idx_alumni_name ON Alumni(name);
CREATE INDEX idx_alumni_current_role ON Alumni(currentRole);
CREATE INDEX idx_alumni_company ON Alumni(company);

-- Status and activity indexes
CREATE INDEX idx_alumni_is_active ON Alumni(isActive);
CREATE INDEX idx_alumni_last_login ON Alumni(lastLoginAt);
CREATE INDEX idx_alumni_created_at ON Alumni(createdAt);
```

### Event Table Indexes

```sql
-- Event filtering and sorting
CREATE INDEX idx_events_start_time ON Event(startTime);
CREATE INDEX idx_events_category ON Event(category);
CREATE INDEX idx_events_organizer ON Event(organizerId);
CREATE INDEX idx_events_is_public ON Event(isPublic);
CREATE INDEX idx_events_created_at ON Event(createdAt);
```

### Post Table Indexes

```sql
-- Post filtering and sorting
CREATE INDEX idx_posts_author ON Post(authorId);
CREATE INDEX idx_posts_created_at ON Post(createdAt);
CREATE INDEX idx_posts_is_public ON Post(isPublic);
CREATE INDEX idx_posts_is_pinned ON Post(isPinned);
CREATE INDEX idx_posts_is_moderated ON Post(isModerated);
```

### Donation Table Indexes

```sql
-- Donation tracking and reporting
CREATE INDEX idx_donations_alumni ON Donation(alumniId);
CREATE INDEX idx_donations_status ON Donation(status);
CREATE INDEX idx_donations_gateway ON Donation(gateway);
CREATE INDEX idx_donations_created_at ON Donation(createdAt);
CREATE INDEX idx_donations_is_recurring ON Donation(isRecurring);
CREATE INDEX idx_donations_next_payment ON Donation(nextPaymentDate);
```

### Activity Table Indexes

```sql
-- Activity logging and audit trails
CREATE INDEX idx_activity_alumni ON Activity(alumniId);
CREATE INDEX idx_activity_type ON Activity(type);
CREATE INDEX idx_activity_timestamp ON Activity(timestamp);
CREATE INDEX idx_activity_ref_id ON Activity(refId);
```

## Query Optimization Strategies

### 1. Efficient Pagination

```typescript
// Use cursor-based pagination for better performance
const alumni = await prisma.alumni.findMany({
  take: limit + 1,
  cursor: cursor ? { id: cursor } : undefined,
  orderBy: { createdAt: 'desc' },
  where: filters,
})
```

### 2. Selective Field Loading

```typescript
// Only select needed fields to reduce data transfer
const alumni = await prisma.alumni.findMany({
  select: {
    id: true,
    name: true,
    photoUrl: true,
    currentRole: true,
    graduationYear: true,
  },
})
```

### 3. Optimized Includes

```typescript
// Use specific includes instead of loading all relations
const alumni = await prisma.alumni.findUnique({
  where: { id },
  include: {
    connections: {
      select: {
        id: true,
        name: true,
        photoUrl: true,
      },
      take: 10, // Limit related records
    },
    _count: {
      select: {
        posts: true,
        donations: true,
      },
    },
  },
})
```

### 4. Batch Operations

```typescript
// Use batch operations for bulk updates
await prisma.alumni.updateMany({
  where: { lastLoginAt: { lt: thirtyDaysAgo } },
  data: { isActive: false },
})
```

## Search Optimization

### Full-Text Search

For better search performance, consider implementing:

1. **PostgreSQL Full-Text Search**
```sql
-- Add full-text search index
CREATE INDEX idx_alumni_search ON Alumni 
USING gin(to_tsvector('english', name || ' ' || COALESCE(bio, '') || ' ' || COALESCE(currentRole, '')));
```

2. **Elasticsearch Integration** (Future Enhancement)
```typescript
// Index alumni data in Elasticsearch for advanced search
const searchResults = await elasticsearchClient.search({
  index: 'alumni',
  body: {
    query: {
      multi_match: {
        query: searchTerm,
        fields: ['name^2', 'bio', 'currentRole', 'skills'],
      },
    },
  },
})
```

## Connection Pooling

### Database Connection Configuration

```typescript
// Optimize Prisma connection pool
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  log: ['query', 'info', 'warn', 'error'],
})

// Connection pool settings in DATABASE_URL
// postgresql://user:password@host:port/db?connection_limit=20&pool_timeout=20
```

## Caching Strategies

### 1. Query Result Caching

```typescript
import { Redis } from 'ioredis'

const redis = new Redis(process.env.REDIS_URL)

// Cache frequently accessed data
async function getCachedAlumni(id: string) {
  const cached = await redis.get(`alumni:${id}`)
  if (cached) {
    return JSON.parse(cached)
  }
  
  const alumni = await prisma.alumni.findUnique({ where: { id } })
  await redis.setex(`alumni:${id}`, 300, JSON.stringify(alumni)) // 5 min cache
  
  return alumni
}
```

### 2. Application-Level Caching

```typescript
// Use React Query for client-side caching
const { data: alumni } = useQuery({
  queryKey: ['alumni', id],
  queryFn: () => api.alumni.getById.query({ id }),
  staleTime: 5 * 60 * 1000, // 5 minutes
  cacheTime: 10 * 60 * 1000, // 10 minutes
})
```

## Performance Monitoring

### 1. Query Performance Tracking

```typescript
// Add query logging middleware
const prisma = new PrismaClient({
  log: [
    {
      emit: 'event',
      level: 'query',
    },
  ],
})

prisma.$on('query', (e) => {
  if (e.duration > 1000) { // Log slow queries (>1s)
    console.warn('Slow query detected:', {
      query: e.query,
      duration: e.duration,
      params: e.params,
    })
  }
})
```

### 2. Database Metrics

Monitor these key metrics:

- **Query Response Time**: Average and 95th percentile
- **Connection Pool Usage**: Active vs available connections
- **Index Usage**: Ensure indexes are being used effectively
- **Cache Hit Ratio**: For Redis and application caches
- **Database Size**: Monitor growth and plan for scaling

### 3. Performance Alerts

Set up alerts for:
- Queries taking longer than 2 seconds
- Connection pool exhaustion
- High CPU/memory usage
- Cache miss rates above 20%

## Database Maintenance

### 1. Regular Maintenance Tasks

```sql
-- Analyze table statistics (run weekly)
ANALYZE Alumni;
ANALYZE Event;
ANALYZE Post;
ANALYZE Donation;

-- Vacuum to reclaim space (run monthly)
VACUUM ANALYZE;

-- Reindex to maintain performance (run quarterly)
REINDEX INDEX CONCURRENTLY idx_alumni_graduation_year;
```

### 2. Data Archival

```typescript
// Archive old activity logs (older than 1 year)
const oneYearAgo = new Date()
oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1)

await prisma.activity.deleteMany({
  where: {
    timestamp: { lt: oneYearAgo },
    type: { in: ['LOGIN', 'LOGOUT', 'PAGE_VIEW'] },
  },
})
```

## Scaling Considerations

### 1. Read Replicas

For high-traffic scenarios, consider read replicas:

```typescript
// Separate read and write connections
const writeDb = new PrismaClient({ datasources: { db: { url: WRITE_DB_URL } } })
const readDb = new PrismaClient({ datasources: { db: { url: READ_DB_URL } } })

// Use read replica for queries
const alumni = await readDb.alumni.findMany()

// Use primary for writes
await writeDb.alumni.create({ data: newAlumni })
```

### 2. Database Sharding

For very large datasets, consider sharding by:
- Graduation year
- Geographic region
- User ID hash

### 3. Horizontal Scaling

Consider moving to distributed databases like:
- PostgreSQL with Citus
- MongoDB with sharding
- Amazon Aurora with read replicas

## Best Practices

### 1. Query Guidelines

- Always use indexes for WHERE clauses
- Limit result sets with LIMIT/OFFSET or cursor pagination
- Use EXISTS instead of IN for subqueries
- Avoid N+1 queries with proper includes
- Use batch operations for bulk updates

### 2. Schema Design

- Normalize data to reduce redundancy
- Use appropriate data types (INT vs BIGINT)
- Consider JSON columns for flexible data
- Use foreign key constraints for data integrity

### 3. Development Practices

- Profile queries during development
- Use database migrations for schema changes
- Test with production-like data volumes
- Monitor query performance in staging

## Migration Strategy

To apply these optimizations:

1. **Create Migration**
```bash
npx prisma migrate dev --name add_performance_indexes
```

2. **Deploy to Production**
```bash
npx prisma migrate deploy
```

3. **Monitor Performance**
- Check query execution plans
- Monitor application response times
- Verify index usage

## Conclusion

These optimizations will significantly improve the performance of the PROTEC Alumni platform. Regular monitoring and maintenance are essential to maintain optimal performance as the platform scales.
