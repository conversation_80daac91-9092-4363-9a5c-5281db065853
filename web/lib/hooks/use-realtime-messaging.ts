import { useEffect, useRef, useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { pusherService, MessageEvent, TypingEvent, OnlineStatusEvent } from '@/lib/services/pusher-service'
import { api } from '@/components/providers/trpc-provider'

interface UseRealtimeMessagingProps {
  conversationId?: string
  enabled?: boolean
}

interface TypingUser {
  userId: string
  userName: string
  timestamp: number
}

export function useRealtimeMessaging({ conversationId, enabled = true }: UseRealtimeMessagingProps = {}) {
  const { data: session } = useSession()
  const [isConnected, setIsConnected] = useState(false)
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([])
  const [onlineUsers, setOnlineUsers] = useState<Set<string>>(new Set())
  const [newMessages, setNewMessages] = useState<MessageEvent[]>([])
  
  const typingTimeoutRef = useRef<Map<string, NodeJS.Timeout>>(new Map())
  const isTypingRef = useRef(false)
  const typingTimeoutSelfRef = useRef<NodeJS.Timeout>()

  // Get current user info
  const currentUserId = session?.user?.email ? 
    // You might want to get the actual user ID from your session or API
    session.user.email : null

  // Initialize Pusher service
  useEffect(() => {
    if (!enabled || !session?.user) return

    // Set current user in pusher service
    if (currentUserId) {
      pusherService.setCurrentUser(currentUserId)
    }

    // Subscribe to presence channel for online status
    pusherService.subscribeToPresence()

    // Subscribe to user-specific channel
    if (currentUserId) {
      pusherService.subscribeToUserChannel(currentUserId)
    }

    return () => {
      // Cleanup on unmount
      pusherService.disconnect()
    }
  }, [session, currentUserId, enabled])

  // Subscribe to conversation
  useEffect(() => {
    if (!enabled || !conversationId || !session?.user) return

    pusherService.subscribeToConversation(conversationId)

    return () => {
      if (conversationId) {
        pusherService.unsubscribeFromConversation(conversationId)
      }
    }
  }, [conversationId, session, enabled])

  // Set up event handlers
  useEffect(() => {
    if (!enabled) return

    const handlersId = `messaging-${Date.now()}`

    // Connection status handler
    pusherService.onConnectionChange(handlersId, (connected) => {
      setIsConnected(connected)
    })

    // Message handler
    pusherService.onMessage(handlersId, (message) => {
      setNewMessages(prev => [...prev, message])
    })

    // Typing handler
    pusherService.onTyping(handlersId, (event) => {
      if (event.userId === currentUserId) return // Ignore own typing

      setTypingUsers(prev => {
        const filtered = prev.filter(user => user.userId !== event.userId)
        
        if (event.isTyping) {
          // Clear existing timeout for this user
          const existingTimeout = typingTimeoutRef.current.get(event.userId)
          if (existingTimeout) {
            clearTimeout(existingTimeout)
          }

          // Set new timeout to remove typing indicator after 3 seconds
          const timeout = setTimeout(() => {
            setTypingUsers(current => current.filter(user => user.userId !== event.userId))
            typingTimeoutRef.current.delete(event.userId)
          }, 3000)

          typingTimeoutRef.current.set(event.userId, timeout)

          return [...filtered, {
            userId: event.userId,
            userName: event.userName,
            timestamp: Date.now()
          }]
        } else {
          // Clear timeout when user stops typing
          const timeout = typingTimeoutRef.current.get(event.userId)
          if (timeout) {
            clearTimeout(timeout)
            typingTimeoutRef.current.delete(event.userId)
          }
          return filtered
        }
      })
    })

    // Online status handler
    pusherService.onStatusChange(handlersId, (event) => {
      setOnlineUsers(prev => {
        const newSet = new Set(prev)
        if (event.isOnline) {
          newSet.add(event.userId)
        } else {
          newSet.delete(event.userId)
        }
        return newSet
      })
    })

    return () => {
      // Cleanup handlers
      pusherService.removeConnectionHandler(handlersId)
      pusherService.removeMessageHandler(handlersId)
      pusherService.removeTypingHandler(handlersId)
      pusherService.removeStatusHandler(handlersId)

      // Clear all typing timeouts
      typingTimeoutRef.current.forEach(timeout => clearTimeout(timeout))
      typingTimeoutRef.current.clear()

      if (typingTimeoutSelfRef.current) {
        clearTimeout(typingTimeoutSelfRef.current)
      }
    }
  }, [enabled, currentUserId])

  // Send message function
  const sendMessage = useCallback(async (content: string, messageType: 'text' | 'image' | 'file' = 'text') => {
    if (!conversationId || !content.trim()) return

    try {
      const response = await fetch('/api/messages/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          conversationId,
          content: content.trim(),
          messageType
        })
      })

      if (!response.ok) {
        throw new Error('Failed to send message')
      }

      const result = await response.json()
      return result.message
    } catch (error) {
      console.error('Error sending message:', error)
      throw error
    }
  }, [conversationId])

  // Typing indicator functions
  const startTyping = useCallback(() => {
    if (!conversationId || !enabled || isTypingRef.current) return

    isTypingRef.current = true
    pusherService.startTyping(conversationId)

    // Auto-stop typing after 3 seconds
    if (typingTimeoutSelfRef.current) {
      clearTimeout(typingTimeoutSelfRef.current)
    }

    typingTimeoutSelfRef.current = setTimeout(() => {
      stopTyping()
    }, 3000)
  }, [conversationId, enabled])

  const stopTyping = useCallback(() => {
    if (!conversationId || !enabled || !isTypingRef.current) return

    isTypingRef.current = false
    pusherService.stopTyping(conversationId)

    if (typingTimeoutSelfRef.current) {
      clearTimeout(typingTimeoutSelfRef.current)
      typingTimeoutSelfRef.current = undefined
    }
  }, [conversationId, enabled])

  // Clear new messages (call this when messages are displayed)
  const clearNewMessages = useCallback(() => {
    setNewMessages([])
  }, [])

  // Check if user is online
  const isUserOnline = useCallback((userId: string) => {
    return onlineUsers.has(userId)
  }, [onlineUsers])

  return {
    // Connection status
    isConnected,
    connectionState: pusherService.getConnectionState(),

    // Messages
    newMessages,
    clearNewMessages,
    sendMessage,

    // Typing indicators
    typingUsers,
    startTyping,
    stopTyping,

    // Online status
    onlineUsers: Array.from(onlineUsers),
    isUserOnline,

    // Utility
    currentUserId
  }
}
