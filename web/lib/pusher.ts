import Pusher from 'pusher'

if (!process.env.PUSHER_APP_ID || !process.env.PUSHER_KEY || !process.env.PUSHER_SECRET || !process.env.PUSHER_CLUSTER) {
  throw new Error('Missing Pusher environment variables. Please set PUSHER_APP_ID, PUSHER_KEY, PUSHER_SECRET, and PUSHER_CLUSTER')
}

export const pusher = new Pusher({
  appId: process.env.PUSHER_APP_ID,
  key: process.env.PUSHER_KEY,
  secret: process.env.PUSHER_SECRET,
  cluster: process.env.PUSHER_CLUSTER,
  useTLS: true,
})

// Helper function to trigger events
export async function triggerPusherEvent(
  channel: string,
  event: string,
  data: any,
  socketId?: string
) {
  try {
    await pusher.trigger(channel, event, data, socketId ? { socket_id: socketId } : undefined)
    console.log(`Pusher event triggered: ${event} on channel: ${channel}`)
  } catch (error) {
    console.error('Failed to trigger Pusher event:', error)
    throw error
  }
}

// Helper function to authenticate private channels
export function authenticateUser(socketId: string, channel: string, userData: any) {
  try {
    const auth = pusher.authorizeChannel(socketId, channel, userData)
    return auth
  } catch (error) {
    console.error('Failed to authenticate Pusher channel:', error)
    throw error
  }
}

// Helper function to authenticate presence channels
export function authenticatePresence(socketId: string, channel: string, userData: any) {
  try {
    const auth = pusher.authorizeChannel(socketId, channel, {
      user_id: userData.id,
      user_info: {
        name: userData.name,
        email: userData.email,
        photoUrl: userData.photoUrl
      }
    })
    return auth
  } catch (error) {
    console.error('Failed to authenticate Pusher presence channel:', error)
    throw error
  }
}

export default pusher
