import type { NextAuthOptions } from "next-auth"
import { PrismaAdapter } from "@next-auth/prisma-adapter"
import GoogleProvider from "next-auth/providers/google"
import GitHubProvider from "next-auth/providers/github"
import EmailProvider from "next-auth/providers/email"
import Credentials<PERSON>rovider from "next-auth/providers/credentials"
import bcrypt from "bcryptjs"
import { prisma } from "./prisma"
import { emailService } from "./services/email-service"

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const alumni = await prisma.alumni.findUnique({
          where: { email: credentials.email.toLowerCase() }
        })

        if (!alumni || !alumni.password) {
          return null
        }

        const isPasswordValid = await bcrypt.compare(credentials.password, alumni.password)

        if (!isPasswordValid) {
          return null
        }

        return {
          id: alumni.id,
          email: alumni.email,
          name: alumni.name,
          image: alumni.photoUrl,
        }
      }
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    GitHubProvider({
      clientId: process.env.AUTH_GITHUB_ID!,
      clientSecret: process.env.AUTH_GITHUB_SECRET!,
    }),
    EmailProvider({
      server: {
        host: process.env.EMAIL_SERVER_HOST,
        port: Number(process.env.EMAIL_SERVER_PORT),
        auth: {
          user: process.env.EMAIL_SERVER_USER,
          pass: process.env.EMAIL_SERVER_PASSWORD,
        },
        secure: process.env.EMAIL_SERVER_SECURE === 'true',
      },
      from: process.env.EMAIL_FROM,
      // Custom sendVerificationRequest function
      sendVerificationRequest: async ({ identifier: email, url }) => {
        try {
          const { host } = new URL(url)
          await emailService.sendMagicLink(email, url, host)
        } catch (error) {
          console.error('Failed to send verification email:', error)
          throw new Error('Failed to send verification email')
        }
      },
    }),
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    // @ts-expect-error - NextAuth callback types
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
        token.email = user.email
        token.name = user.name
        token.picture = user.image
      }

      // Always fetch the latest role from database
      if (token.email) {
        const alumni = await prisma.alumni.findUnique({
          where: { email: (token.email as string).toLowerCase() },
          select: { role: true, isActive: true }
        })

        if (alumni) {
          token.role = alumni.role
          token.isActive = alumni.isActive
        }
      }

      return token
    },
    // @ts-expect-error - NextAuth callback types
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string
        session.user.email = token.email as string
        session.user.name = token.name as string
        session.user.image = token.picture as string
        session.user.role = token.role as string
        session.user.isActive = token.isActive as boolean
      }
      return session
    },
    // @ts-expect-error - NextAuth callback types
    async signIn({ user, account }) {
      try {
        // Handle credentials provider (email/password)
        if (account?.provider === "credentials") {
          // User is already authenticated by the authorize function
          return true
        }

        // Handle OAuth providers (Google, GitHub)
        if (account?.provider === "google" || account?.provider === "github") {
          // Check if alumni exists, if not create one
          const existingAlumni = await prisma.alumni.findUnique({
            where: { email: user.email!.toLowerCase() }
          })

          if (!existingAlumni) {
            // Create new alumni record
            await prisma.alumni.create({
              data: {
                email: user.email!.toLowerCase(),
                name: user.name || "",
                photoUrl: user.image,
                graduationYear: new Date().getFullYear(), // Default, can be updated later
                programmes: [],
                skills: [],
                province: "",
                city: "",
                privacy: {
                  showEmail: false,
                  showPhone: false,
                  showLocation: true,
                  showConnections: true,
                },
              }
            })

            // Send welcome email for new users
            try {
              await emailService.sendWelcomeEmail(user.email!, user.name || 'Alumni')
            } catch (emailError) {
              console.error("Failed to send welcome email:", emailError)
              // Don't fail the sign-in if email fails
            }
          }
        }

        // Handle email magic link sign-in
        if (account?.provider === "email") {
          // Check if alumni exists, if not create one
          const existingAlumni = await prisma.alumni.findUnique({
            where: { email: user.email!.toLowerCase() }
          })

          if (!existingAlumni) {
            // Create new alumni record for email sign-in
            await prisma.alumni.create({
              data: {
                email: user.email!.toLowerCase(),
                name: user.name || "",
                photoUrl: user.image,
                graduationYear: new Date().getFullYear(), // Default, can be updated later
                programmes: [],
                skills: [],
                province: "",
                city: "",
                privacy: {
                  showEmail: false,
                  showPhone: false,
                  showLocation: true,
                  showConnections: true,
                },
              }
            })

            // Send welcome email for new users
            try {
              await emailService.sendWelcomeEmail(user.email!, user.name || 'Alumni')
            } catch (emailError) {
              console.error("Failed to send welcome email:", emailError)
              // Don't fail the sign-in if email fails
            }
          }
        }

        return true
      } catch (error) {
        console.error("Error in signIn callback:", error)
        return false
      }
    },
  },
  pages: {
    signIn: "/auth/signin",
    signUp: "/auth/signup",
    error: "/auth/error",
    verifyRequest: "/auth/verify-request",
  },
  secret: process.env.NEXTAUTH_SECRET,
}
