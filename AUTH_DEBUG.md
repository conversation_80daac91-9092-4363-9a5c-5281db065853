# Authentication Debug Guide

## Issue Fixed: Email Case Sensitivity Mismatch

### Problem
Registration was succeeding but signin was failing due to email case sensitivity issues:

1. **Registration API**: Stored emails as `email.toLowerCase()` (line 64 in register route)
2. **Signin Process**: Searched for emails without converting to lowercase (line 26 in auth.ts)

### Solution Applied
Fixed email case consistency throughout the authentication flow:

1. ✅ **Credentials Provider**: Fixed email lookup to use `credentials.email.toLowerCase()`
2. ✅ **OAuth Providers**: Fixed email lookup to use `user.email!.toLowerCase()`
3. ✅ **Email Provider**: Fixed email lookup to use `user.email!.toLowerCase()`
4. ✅ **JWT Callback**: Fixed email lookup to use `(token.email as string).toLowerCase()`
5. ✅ **Frontend Forms**: Fixed all signin/signup forms to send `email.toLowerCase()`

### Files Modified

#### `/web/lib/auth.ts`
- Line 26: Fixed credentials provider email lookup
- Line 131: Fixed OAuth provider email lookup
- Line 138: Fixed OAuth provider email storage
- Line 169: Fixed email provider email lookup  
- Line 176: Fixed email provider email storage
- Line 94: Fixed JWT callback email lookup

#### `/web/app/auth/signin/page.tsx`
- Line 35: Fixed credentials signin email
- Line 60: Fixed email signin

#### `/web/app/auth/signup/page.tsx`
- Line 76: Fixed credentials signup email
- Line 107: Fixed email signup

### Testing Steps

1. **Clear existing test data** (if any):
   ```sql
   DELETE FROM alumni WHERE email LIKE '%test%';
   DELETE FROM accounts WHERE provider = 'credentials';
   DELETE FROM sessions WHERE expires < NOW();
   ```

2. **Test Registration**:
   - Go to `/auth/signup`
   - Register with email: `<EMAIL>`
   - Should succeed and automatically sign in

3. **Test Signin**:
   - Go to `/auth/signin`
   - Sign in with email: `<EMAIL>` (different case)
   - Should work now with case-insensitive matching

4. **Test Case Variations**:
   - Register: `<EMAIL>`
   - Sign in: `<EMAIL>`
   - Should work seamlessly

### Additional Debugging Tools

#### Check Database Records
```sql
SELECT id, email, name, password IS NOT NULL as has_password, created_at 
FROM alumni 
WHERE email LIKE '%test%' 
ORDER BY created_at DESC;
```

#### Test Email Normalization
```javascript
// In browser console or test file
const testEmails = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
];

testEmails.forEach(email => {
  console.log(`${email} -> ${email.toLowerCase()}`);
});
```

### Expected Behavior Now

1. **Registration**: Creates user with normalized email
2. **Signin**: Finds user regardless of email case
3. **OAuth**: Consistent case handling
4. **Magic Links**: Consistent case handling
5. **JWT Tokens**: Consistent case handling

### Error Handling Improvements

The auth system now handles:
- Case-insensitive email matching
- Consistent email storage
- Proper error messages
- Graceful fallbacks

### Security Considerations

- Email normalization prevents duplicate accounts
- Case-insensitive matching improves UX
- Maintains secure password hashing
- Preserves OAuth security

### Next Steps

1. Test the registration and signin flow
2. Verify OAuth providers work correctly
3. Test magic link authentication
4. Monitor for any additional issues

### Common Issues to Watch For

1. **Database Connection**: Ensure Prisma is properly connected
2. **Environment Variables**: Check NEXTAUTH_SECRET is set
3. **Password Hashing**: Verify bcrypt is working correctly
4. **Session Management**: Ensure JWT tokens are properly signed

---

## Quick Test Commands

```bash
# Start development server
npm run dev

# Test registration
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPassword123",
    "name": "Test User",
    "graduationYear": 2023,
    "programmes": ["Computer Science"],
    "province": "Gauteng",
    "city": "Johannesburg"
  }'

# Check database
npx prisma studio
```

The authentication flow should now work seamlessly with case-insensitive email handling!